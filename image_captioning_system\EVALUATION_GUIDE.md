# Image Captioning Evaluation System Guide

## Overview

This guide explains how to use the comprehensive evaluation system for the image captioning model, including automatic evaluation workflows and ground truth caption management.

## Features

### 1. Evaluation Metrics

The system supports the following industry-standard metrics:

- **BLEU-1, BLEU-2, BLEU-3, BLEU-4**: N-gram precision metrics
- **METEOR**: Semantic similarity with synonyms and stemming
- **ROUGE-L**: Longest common subsequence metric
- **CIDEr**: Consensus-based metric for image descriptions
- **SPICE**: Semantic understanding evaluation

### 2. Evaluation Modes

#### Manual Evaluation
- Access via the "Evaluation" link in the navigation
- Manually enter predicted captions and reference captions
- Suitable for one-off evaluations or custom reference captions

#### Automatic Evaluation
- Automatically triggered after audio playback (if ground truth captions are available)
- Uses pre-loaded reference captions from `captions.txt`
- Streamlined workflow for systematic evaluation

## Ground Truth Caption System

### File Format: `captions.txt`

The system uses a simple pipe-separated format for ground truth captions:

```
# Ground Truth Captions File
# Format: filename|caption1|caption2|caption3|...
# Lines starting with # are comments

image1.jpg|A person riding a bicycle|Someone cycling down the street|A cyclist on a bike
image2.png|A dog playing in the park|A canine running on grass|A pet enjoying outdoor time
```

### File Location

Place the `captions.txt` file in the `data/` directory:
```
image_captioning_system/
├── data/
│   └── captions.txt
├── app/
├── src/
└── ...
```

### Adding New Captions

1. **Manual Addition**: Edit the `captions.txt` file directly
2. **Format**: `filename|caption1|caption2|caption3|...`
3. **Multiple References**: Add multiple reference captions separated by pipes (`|`)
4. **Comments**: Use `#` at the beginning of lines for comments

### Example Entry

```
tourist_photo.jpg|Tourists stroll past intricately carved archways|People walk by ornate stone arches|Visitors pass decorative architectural features
```

## Automatic Evaluation Workflow

### Step 1: Upload Image
1. Go to the main page
2. Upload an image file
3. Wait for caption generation

### Step 2: Audio Playback (Optional)
1. Listen to the generated caption via audio controls
2. After audio finishes, you'll be prompted for automatic evaluation (if ground truth exists)

### Step 3: Automatic Evaluation
1. If reference captions exist, you'll see an "Auto-Evaluate Caption" button
2. Click the button or wait for auto-redirect after audio
3. The system automatically loads reference captions and runs evaluation
4. View comprehensive metric scores

### Step 4: Results Analysis
- Review BLEU scores for n-gram precision
- Check METEOR for semantic similarity
- Analyze ROUGE-L for sequence matching
- Examine CIDEr and SPICE for advanced metrics

## Manual Evaluation Workflow

### Step 1: Access Evaluation Page
- Click "Evaluation" in the navigation menu
- Or click "Manual Evaluation" from results page

### Step 2: Enter Captions
1. **Predicted Caption**: Enter the model-generated caption
2. **Reference Captions**: Add one or more ground truth captions
3. Use the "+" button to add multiple references

### Step 3: Run Evaluation
1. Click "Evaluate Caption"
2. Wait for processing
3. Review detailed metric scores

## Installation Requirements

### Required Packages

The evaluation system requires additional Python packages:

```bash
# Install in your virtual environment
pip install rouge-score
pip install pycocoevalcap
pip install nltk
```

### NLTK Data

The system automatically downloads required NLTK data:
- `punkt` tokenizer
- `wordnet` corpus

## Troubleshooting

### Missing Metrics

If you see warnings about unavailable metrics:

1. **ROUGE-L unavailable**: Install `rouge-score` package
2. **CIDEr/SPICE unavailable**: Install `pycocoevalcap` package
3. **METEOR issues**: Ensure NLTK and wordnet are properly installed

### Ground Truth Issues

1. **No reference captions found**: Check `captions.txt` file format and location
2. **File not loading**: Verify file encoding is UTF-8
3. **Filename mismatch**: Ensure exact filename match (case-sensitive)

### Performance Tips

1. **Multiple References**: Use 3-5 reference captions per image for better evaluation
2. **Diverse Captions**: Include varied phrasings in reference captions
3. **Regular Updates**: Keep `captions.txt` updated with new images

## API Endpoints

### Evaluation Endpoints

- `GET /evaluate` - Manual evaluation page
- `GET /auto_evaluate` - Automatic evaluation page
- `POST /evaluate_caption` - Evaluate caption against references

### Parameters

```json
{
    "predicted_caption": "Generated caption text",
    "reference_captions": ["Reference 1", "Reference 2"],
    "filename": "image.jpg"  // Optional, for auto-loading references
}
```

## Best Practices

### Caption Quality
1. Use descriptive, accurate reference captions
2. Include multiple perspectives for each image
3. Maintain consistent style across references

### Evaluation Workflow
1. Test with known good/bad captions first
2. Use automatic evaluation for systematic testing
3. Compare metrics across different model versions

### File Management
1. Keep `captions.txt` under version control
2. Backup caption files regularly
3. Document caption sources and creation process

## Example Usage

### Complete Workflow Example

1. **Prepare Ground Truth**:
   ```
   # In data/captions.txt
   beach_scene.jpg|People enjoying a sunny day at the beach|Beachgoers relaxing on sandy shore|Sunny beach with people and umbrellas
   ```

2. **Upload Image**: Upload `beach_scene.jpg`

3. **Generate Caption**: System produces "A group of people on a beach"

4. **Automatic Evaluation**: System finds reference captions and evaluates:
   - BLEU-1: 0.7500
   - BLEU-4: 0.4123
   - METEOR: 0.6234
   - ROUGE-L: 0.5678

5. **Analysis**: Good BLEU-1 indicates word overlap, moderate BLEU-4 suggests room for improvement in longer sequences

This comprehensive evaluation system enables systematic assessment of caption quality and model performance improvement over time.
