# Gemini API Integration Guide

This guide explains how to integrate Google's Gemini API into your image captioning system, allowing you to replace your custom-trained models with Gemini's powerful multimodal capabilities.

## Overview

The Gemini integration provides:
- **Drop-in replacement** for your existing models
- **Unified interface** that works with both local models and Gemini API
- **Seamless fallback** from Gemini to local models if needed
- **Preserved functionality** for OCR, audio output, and web interface
- **Enhanced caption quality** using Gemini's advanced multimodal understanding

## Quick Start

### 1. Install Dependencies

```bash
pip install google-generativeai>=0.3.0 requests>=2.31.0
```

Or run the automated setup:

```bash
python setup_gemini.py
```

### 2. Get Gemini API Key

1. Go to [Google AI Studio](https://makersuite.google.com/app/apikey)
2. Create a new API key
3. Copy the API key

### 3. Configure API Key

**Option A: Environment Variable**
```bash
export GEMINI_API_KEY=your_api_key_here
```

**Option B: .env File**
Create a `.env` file in the project root:
```
GEMINI_API_KEY=your_api_key_here
DEFAULT_BACKEND=gemini
```

### 4. Test the Integration

```bash
# Test with a single image
python src/predict_unified.py --image path/to/your/image.jpg

# Test with the web application
python app/app.py
```

## Usage Examples

### Command Line Interface

```bash
# Basic usage with Gemini
python src/predict_unified.py --image sample.png --backend gemini

# Generate multiple captions
python src/predict_unified.py --image sample.png --num-captions 3

# Use different caption styles
python src/predict_unified.py --image sample.png --style detailed

# Disable OCR or audio
python src/predict_unified.py --image sample.png --no-ocr --no-audio

# Fallback to local models if Gemini fails
python src/predict_unified.py --image sample.png --backend auto
```

### Python API Usage

```python
from src.models.unified_captioner import UnifiedImageCaptioner

# Initialize with Gemini
captioner = UnifiedImageCaptioner(
    backend="gemini",
    gemini_api_key="your_api_key_here"
)

# Generate caption
caption = captioner.generate_caption("path/to/image.jpg")
print(f"Caption: {caption}")

# Generate multiple captions
captions = captioner.generate_multiple_captions(
    "path/to/image.jpg", 
    num_captions=3
)

# Check backend status
info = captioner.get_backend_info()
print(f"Using backend: {info['backend']}")
```

### Web Application

The Flask web application automatically uses the unified captioner:

1. Start the app: `python app/app.py`
2. Open `http://localhost:5000`
3. Upload an image and get captions powered by Gemini

## Configuration Options

### Backend Selection

- **`gemini`**: Use only Gemini API
- **`local`**: Use only local models
- **`auto`**: Try Gemini first, fallback to local models

### Caption Styles (Gemini only)

- **`descriptive`**: Balanced, informative captions
- **`concise`**: Brief, to-the-point descriptions
- **`detailed`**: Comprehensive, detailed descriptions

### Environment Variables

```bash
# Required
GEMINI_API_KEY=your_api_key_here

# Optional
DEFAULT_BACKEND=gemini          # gemini, local, or auto
DEFAULT_MODEL_TYPE=gru_lstm     # For local fallback
GEMINI_MODEL_NAME=gemini-2.5-flash  # Gemini model to use (latest stable)
```

## Architecture Details

### Unified Interface

The `UnifiedImageCaptioner` class provides a consistent interface:

```python
class UnifiedImageCaptioner:
    def generate_caption(self, image_path, **kwargs) -> str
    def generate_multiple_captions(self, image_path, num_captions=3) -> List[str]
    def get_backend_info(self) -> Dict[str, Any]
    def switch_backend(self, new_backend, **kwargs)
```

### Gemini Adapter

The `GeminiImageCaptioner` class handles:
- API authentication and configuration
- Image preprocessing and optimization
- Prompt engineering for different caption styles
- Error handling and retry logic
- Response caching for efficiency

### Preserved Features

All existing features continue to work:
- **OCR Integration**: Text detection using Tesseract and EasyOCR
- **Audio Output**: Text-to-speech using gTTS
- **Web Interface**: Flask application with file upload
- **Beam Search Simulation**: Multiple caption generation
- **Attention Visualization**: (Note: Not available with Gemini)

## Migration from Local Models

### Automatic Migration

The system automatically detects available backends:

```python
# This will try Gemini first, then fall back to local models
captioner = UnifiedImageCaptioner(backend="auto")
```

### Manual Migration

Replace your existing prediction code:

**Before:**
```python
from src.predict import generate_caption_gru_lstm, load_gru_lstm_model

encoder, decoder = load_gru_lstm_model(model_dir)
caption = generate_caption_gru_lstm(image_path, encoder, decoder, tokenizer)
```

**After:**
```python
from src.models.unified_captioner import UnifiedImageCaptioner

captioner = UnifiedImageCaptioner(backend="gemini")
caption = captioner.generate_caption(image_path)
```

### Flask App Migration

The Flask app has been updated to use the unified captioner automatically. No changes needed to your templates or frontend code.

## Performance Considerations

### API Limits and Costs

- **Rate Limits**: Gemini has API rate limits (check Google's documentation)
- **Costs**: Gemini API usage incurs costs (see Google's pricing)
- **Caching**: The system caches recent results to reduce API calls

### Optimization Tips

1. **Use appropriate image sizes**: Images are automatically resized to 1024px max
2. **Batch processing**: For multiple images, consider rate limiting
3. **Fallback strategy**: Use `backend="auto"` for reliability
4. **Cache management**: Clear cache periodically if processing many images

## Troubleshooting

### Common Issues

**API Key Issues:**
```bash
# Check if API key is set
echo $GEMINI_API_KEY

# Test API key
python -c "import google.generativeai as genai; genai.configure(api_key='your_key'); print('API key works!')"
```

**Import Errors:**
```bash
# Install missing dependencies
pip install google-generativeai requests

# Check installation
python -c "import google.generativeai; print('Import successful')"
```

**Backend Fallback:**
```python
# Check which backend is being used
captioner = UnifiedImageCaptioner(backend="auto")
info = captioner.get_backend_info()
print(f"Active backend: {info['backend']}")
```

### Error Messages

- **"Gemini API key required"**: Set the `GEMINI_API_KEY` environment variable
- **"Error calling Gemini API"**: Check internet connection and API key validity
- **"Falling back to local models"**: Gemini failed, using local models instead

## Advanced Usage

### Custom Prompts

Modify the prompt generation in `GeminiImageCaptioner._create_prompt()`:

```python
def _create_prompt(self, style="descriptive", **kwargs):
    # Customize prompts for your specific use case
    if style == "medical":
        return "Describe this medical image in detail for healthcare professionals..."
    # ... existing code
```

### Multiple API Keys

For high-volume usage, implement API key rotation:

```python
api_keys = ["key1", "key2", "key3"]
captioner = UnifiedImageCaptioner(
    backend="gemini",
    gemini_api_key=api_keys[0]  # Implement rotation logic
)
```

### Custom Models

Use different Gemini models:

```python
captioner = UnifiedImageCaptioner(
    backend="gemini",
    gemini_api_key="your_key"
)
# Modify captioner.gemini_captioner.model_name = "gemini-1.5-pro"
```

## Support

For issues with:
- **Gemini API**: Check [Google AI documentation](https://ai.google.dev/)
- **Integration code**: Review the source code in `src/models/`
- **Local models**: Refer to the original training documentation

## Next Steps

1. **Test thoroughly** with your specific images and use cases
2. **Monitor API usage** and costs
3. **Optimize prompts** for your domain-specific needs
4. **Consider hybrid approaches** using both Gemini and local models
5. **Implement monitoring** for production deployments
