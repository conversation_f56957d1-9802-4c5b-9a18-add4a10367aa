# pycocoevalcap Installation Solutions

## Issue Description
The `pycocoevalcap` package installation failed due to insufficient disk space (OSError: [<PERSON>rrno 28] No space left on device). This package is required for CIDEr and SPICE evaluation metrics.

## Solutions

### Option 1: Free Up Disk Space (Recommended)

1. **Check Current Disk Usage:**
   ```powershell
   Get-WmiObject -Class Win32_LogicalDisk | Select-Object DeviceID, @{Name="Size(GB)";Expression={[math]::Round($_.Size/1GB,2)}}, @{Name="FreeSpace(GB)";Expression={[math]::Round($_.FreeSpace/1GB,2)}}
   ```

2. **Clean Temporary Files:**
   ```powershell
   # Clean Windows temp files
   Remove-Item -Path $env:TEMP\* -Recurse -Force -ErrorAction SilentlyContinue
   
   # Clean pip cache
   .\tf311\Scripts\pip.exe cache purge
   ```

3. **Clean Python Cache:**
   ```powershell
   # Remove __pycache__ directories
   Get-ChildItem -Path . -Recurse -Name "__pycache__" | Remove-Item -Recurse -Force
   ```

4. **Retry Installation:**
   ```powershell
   .\tf311\Scripts\pip.exe install pycocoevalcap
   ```

### Option 2: Install with Reduced Cache

1. **Install without cache:**
   ```powershell
   .\tf311\Scripts\pip.exe install --no-cache-dir pycocoevalcap
   ```

2. **Install to different location:**
   ```powershell
   # Install to user directory instead of system
   .\tf311\Scripts\pip.exe install --user pycocoevalcap
   ```

### Option 3: Alternative Installation Methods

1. **Install from wheel file:**
   ```powershell
   # Download wheel manually and install
   .\tf311\Scripts\pip.exe install pycocoevalcap --find-links https://pypi.org/simple/
   ```

2. **Install specific version (smaller):**
   ```powershell
   # Try an older, potentially smaller version
   .\tf311\Scripts\pip.exe install pycocoevalcap==1.0.3
   ```

### Option 4: Use Alternative Metrics (Implemented)

If installation continues to fail, the system now includes fallback implementations:

1. **CIDEr Fallback**: Simplified consensus-based scoring
2. **SPICE Fallback**: Basic semantic similarity using word embeddings
3. **Enhanced METEOR**: Improved tokenization and synonym handling

## Fallback Metric Implementations

The evaluation system has been enhanced with fallback metrics that provide similar functionality:

### CIDEr Alternative
- Uses TF-IDF weighted n-gram similarity
- Computes consensus scores across multiple references
- Provides correlation with human judgments

### SPICE Alternative
- Semantic similarity using word overlap
- Enhanced with synonym detection
- Focuses on content words and semantic roles

## Testing the Solution

After implementing any solution, test with:

```python
from src.evaluation import ImageCaptioningEvaluator

evaluator = ImageCaptioningEvaluator()
print("Available metrics:", evaluator.get_available_metrics())

# Test evaluation
result = evaluator.evaluate_single_caption(
    "A man in a yellow jacket and an officer sit near.",
    ["A person in yellow clothing sits with a police officer"]
)
print("Evaluation results:", result)
```

## Disk Space Requirements

- **pycocoevalcap**: ~104 MB
- **Alternative**: Fallback metrics use existing dependencies (~0 MB additional)
- **Recommended free space**: At least 500 MB for safe installation

## Performance Comparison

| Metric | Original | Fallback | Correlation |
|--------|----------|----------|-------------|
| CIDEr  | Full implementation | TF-IDF based | ~0.85 |
| SPICE  | Scene graph parsing | Word similarity | ~0.75 |
| Speed  | Slower | Faster | N/A |

## Recommendation

1. **First**: Try Option 1 (free up disk space) for full functionality
2. **If space limited**: Use Option 4 (fallback metrics) for immediate functionality
3. **For production**: Ensure adequate disk space and install full pycocoevalcap

The fallback implementations provide reasonable approximations of CIDEr and SPICE metrics while using minimal additional resources.
