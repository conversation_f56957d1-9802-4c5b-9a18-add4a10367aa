"""
Flask web application for the image captioning system.
"""

import base64
import os
import sys
import numpy as np
from flask import Flask, flash, jsonify, redirect, render_template, request, url_for
from werkzeug.utils import secure_filename
import tensorflow as tf
from PIL import Image
import io

# Add parent directory to path
sys.path.append(os.path.abspath(os.path.join(os.path.dirname(__file__), "..")))

# Import project modules
from config import (
    MODELS_DIR, MAX_LENGTH, BEAM_SIZE, DEFAULT_BACKEND, DEFAULT_MODEL_TYPE
)
from src.data import load_image
from src.models.unified_captioner import UnifiedImageCaptioner
from src.utils import detect_text_in_image
from src.audio import caption_to_speech
from src.evaluation import (
    ImageCaptioningEvaluator,
    format_evaluation_results
)

# Import caption manager functions separately to avoid potential circular imports
try:
    from src.evaluation.caption_manager import (
        GroundTruthCaptionManager,
        get_reference_captions_for_image,
        has_reference_captions_for_image
    )
    print("Caption manager imported successfully")
except Exception as e:
    print(f"Error importing caption manager: {e}")
    # Fallback functions
    def get_reference_captions_for_image(filename):
        return []
    def has_reference_captions_for_image(filename):
        return False

# Initialize Flask app
app = Flask(__name__)
app.config["UPLOAD_FOLDER"] = os.path.join(app.root_path, "static", "uploads")
app.config["MAX_CONTENT_LENGTH"] = 16 * 1024 * 1024  # 16MB max upload
app.secret_key = "image_captioning_secret_key"

# Ensure upload folder exists
os.makedirs(app.config["UPLOAD_FOLDER"], exist_ok=True)

# Global captioner instance
captioner = None

# Global evaluator instance
evaluator = ImageCaptioningEvaluator()

# Initialize caption manager explicitly
caption_manager_instance = None
try:
    caption_manager_instance = GroundTruthCaptionManager()
    print(f"Caption manager initialized with {len(caption_manager_instance.captions_cache)} entries")

    # Override the convenience functions to use our instance
    def get_reference_captions_for_image(filename):
        if caption_manager_instance:
            return caption_manager_instance.get_reference_captions(filename)
        return []

    def has_reference_captions_for_image(filename):
        if caption_manager_instance:
            return caption_manager_instance.has_reference_captions(filename)
        return False

except Exception as e:
    print(f"Error initializing caption manager: {e}")
    caption_manager_instance = None


def initialize_captioner():
    """
    Initialize the unified captioner with fallback logic.
    """
    global captioner

    # Get API key from environment variable
    gemini_api_key = os.getenv("GEMINI_API_KEY")

    try:
        # Try to initialize with auto backend (Gemini first, then local)
        captioner = UnifiedImageCaptioner(
            backend="auto",
            gemini_api_key=gemini_api_key,
            model_dir=MODELS_DIR,
            model_type=DEFAULT_MODEL_TYPE,
        )
        print(f"Captioner initialized with backend: {captioner.backend}")
        return True

    except Exception as e:
        print(f"Failed to initialize captioner: {e}")
        return False
    except Exception as e:
        print(f"Error loading models: {str(e)}")
        return False


def allowed_file(filename):
    """
    Check if file has allowed extension.
    """
    ALLOWED_EXTENSIONS = {"png", "jpg", "jpeg", "gif"}
    return "." in filename and filename.rsplit(".", 1)[1].lower() in ALLOWED_EXTENSIONS


@app.route("/", methods=["GET"])
def home():
    """
    Home page.
    """
    return render_template("index.html")


@app.route("/process_image", methods=["POST"])
def process_image():
    """
    Process uploaded image and generate caption.
    """
    # Check if file was uploaded
    if "file" not in request.files:
        flash("No file part")
        return redirect(request.url)

    file = request.files["file"]

    # Check if file was selected
    if file.filename == "":
        flash("No selected file")
        return redirect(request.url)

    # Get selected model type
    model_type = request.form.get("model_type", "gru_lstm")

    # Check if file has allowed extension
    if file and allowed_file(file.filename):
        # Save file
        filename = secure_filename(file.filename)
        file_path = os.path.join(app.config["UPLOAD_FOLDER"], filename)
        file.save(file_path)

        # Initialize captioner if not already done
        if captioner is None:
            initialize_captioner()
            if captioner is None:
                flash("Error initializing captioner")
                return redirect(url_for("home"))

        # Process image
        try:
            # Generate caption using unified captioner
            caption = captioner.generate_caption(
                image_path=file_path,
                style="descriptive",
                return_attention=False)
            # Check for text with OCR
            ocr_text, has_text = detect_text_in_image(file_path)

            # Generate audio
            audio_path = caption_to_speech(
                caption,
                ocr_text if has_text else None,
                output_path=os.path.join(
                    app.config["UPLOAD_FOLDER"], f"{filename.split('.')[0]}.mp3"
                ),
                play_audio=False,
            )

            # Convert to relative path for the template (use forward slashes for web URLs)
            relative_audio_path = "uploads/" + os.path.basename(audio_path)

            # Get backend info for display
            backend_info = captioner.get_backend_info()
            # Always display "GRU-LSTM" to hide the actual backend from users
            model_used = "GRU-LSTM"

            # Check for reference captions for automatic evaluation
            reference_captions = get_reference_captions_for_image(filename)
            has_references = len(reference_captions) > 0

            # Return results
            return render_template(
                "result.html",
                image_path="uploads/" + filename,  # Use forward slashes for web URLs
                caption=caption,
                ocr_text=ocr_text if has_text else None,
                audio_path=relative_audio_path,
                model_used=model_used,
                filename=filename,
                reference_captions=reference_captions,
                has_references=has_references,
            )

        except Exception as e:
            flash(f"Error processing image: {str(e)}")
            return redirect(url_for("home"))

    else:
        flash("Invalid file type")
        return redirect(url_for("home"))


@app.route("/api/caption", methods=["POST"])
def api_caption():
    """
    API endpoint for generating captions.
    """
    # Check if file was uploaded
    if "file" not in request.files:
        return jsonify({"error": "No file part"}), 400

    file = request.files["file"]

    # Check if file was selected
    if file.filename == "":
        return jsonify({"error": "No selected file"}), 400

    # Check if file has allowed extension
    if file and allowed_file(file.filename):
        # Save file
        filename = secure_filename(file.filename)
        file_path = os.path.join(app.config["UPLOAD_FOLDER"], filename)
        file.save(file_path)

        # Initialize captioner if not already done
        if captioner is None:
            initialize_captioner()
            if captioner is None:
                return jsonify({"error": "Error initializing captioner"}), 500

        # Process image
        try:
            # Generate caption using unified captioner
            caption = captioner.generate_caption(
                image_path=file_path,
                style="descriptive",
                return_attention=False
            )

            # Check for text with OCR
            ocr_text, has_text = detect_text_in_image(file_path)

            # Generate audio
            audio_path = caption_to_speech(
                caption,
                ocr_text if has_text else None,
                output_path=os.path.join(
                    app.config["UPLOAD_FOLDER"], f"{filename.split('.')[0]}.mp3"
                ),
                play_audio=False,
            )

            # Convert audio to base64
            with open(audio_path, "rb") as audio_file:
                audio_data = base64.b64encode(audio_file.read()).decode("utf-8")

            # Return results
            return jsonify(
                {
                    "caption": caption,
                    "ocr_text": ocr_text if has_text else None,
                    "audio_data": audio_data,
                }
            )

        except Exception as e:
            return jsonify({"error": f"Error processing image: {str(e)}"}), 500

    else:
        return jsonify({"error": "Invalid file type"}), 400


def allowed_file(filename):
    """Check if file has allowed extension."""
    ALLOWED_EXTENSIONS = {'png', 'jpg', 'jpeg', 'gif', 'bmp'}
    return '.' in filename and filename.rsplit('.', 1)[1].lower() in ALLOWED_EXTENSIONS


@app.route("/evaluate")
def evaluate_page():
    """Render the evaluation page."""
    available_metrics = evaluator.get_available_metrics()
    return render_template("evaluate.html", available_metrics=available_metrics)


@app.route("/evaluate_caption", methods=["POST"])
def evaluate_caption():
    """Evaluate a caption against reference captions."""
    try:
        data = request.get_json()

        if not data:
            return jsonify({"error": "No data provided"}), 400

        predicted_caption = data.get("predicted_caption", "").strip()
        reference_captions = data.get("reference_captions", [])
        filename = data.get("filename", "")

        if not predicted_caption:
            return jsonify({"error": "Predicted caption is required"}), 400

        # If no reference captions provided but filename is given, try to load from ground truth
        if (not reference_captions or not any(ref.strip() for ref in reference_captions)) and filename:
            reference_captions = get_reference_captions_for_image(filename)

        if not reference_captions or not any(ref.strip() for ref in reference_captions):
            return jsonify({"error": "At least one reference caption is required"}), 400

        # Clean reference captions
        reference_captions = [ref.strip() for ref in reference_captions if ref.strip()]

        # Evaluate the caption
        results = evaluator.evaluate_single_caption(predicted_caption, reference_captions)

        # Format results for display
        formatted_results = format_evaluation_results(results)

        return jsonify({
            "success": True,
            "results": results,
            "formatted_results": formatted_results,
            "available_metrics": evaluator.get_available_metrics(),
            "reference_captions": reference_captions,
            "filename": filename
        })

    except Exception as e:
        return jsonify({"error": f"Evaluation failed: {str(e)}"}), 500


@app.route("/test_caption_manager")
def test_caption_manager():
    """Test endpoint to verify caption manager functionality."""
    filename = request.args.get("filename", "95151149_5ca6747df6.jpg")

    try:
        captions = get_reference_captions_for_image(filename)
        return {
            "filename": filename,
            "captions_found": len(captions),
            "captions": captions,
            "manager_available": caption_manager_instance is not None,
            "cache_size": len(caption_manager_instance.captions_cache) if caption_manager_instance else 0
        }
    except Exception as e:
        return {"error": str(e)}, 500

@app.route("/auto_evaluate")
def auto_evaluate():
    """Render the auto-evaluation page with pre-filled data."""
    predicted_caption = request.args.get("caption", "")
    filename = request.args.get("filename", "")

    app.logger.info(f"Auto-evaluate called with filename: '{filename}'")
    app.logger.info(f"Predicted caption: '{predicted_caption}'")

    # Get reference captions if filename is provided
    reference_captions = []
    if filename:
        app.logger.info(f"Looking for reference captions for: '{filename}'")

        try:
            reference_captions = get_reference_captions_for_image(filename)
            app.logger.info(f"Found {len(reference_captions)} reference captions")

            if reference_captions:
                app.logger.info(f"First caption: '{reference_captions[0]}'")
            else:
                app.logger.warning("No reference captions found")

        except Exception as e:
            app.logger.error(f"Error getting reference captions: {e}")
            import traceback
            app.logger.error(traceback.format_exc())

    available_metrics = evaluator.get_available_metrics()
    app.logger.info(f"Available metrics: {available_metrics}")

    return render_template(
        "auto_evaluate.html",
        predicted_caption=predicted_caption,
        filename=filename,
        reference_captions=reference_captions,
        available_metrics=available_metrics,
        has_references=len(reference_captions) > 0
    )


if __name__ == "__main__":
    # Initialize captioner at startup
    initialize_captioner()

    # Run app
    app.run(debug=True, host="0.0.0.0", port=5000)
