<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title>Auto-Evaluation | Assistive AI</title>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;600;700&display=swap" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        :root {
            --primary: #6b5b95;
            --secondary: #b8a9c9;
            --light-bg: #f0f2f5;
            --dark-text: #333;
            --border-radius: 8px;
            --transition: 0.3s ease;
        }
        body {
            font-family: 'Inter', sans-serif;
            background: var(--light-bg);
            color: var(--dark-text);
        }
        .navbar {
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .evaluation-section {
            background-color: #fff;
            border-radius: var(--border-radius);
            padding: 40px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .evaluation-title {
            font-size: 2rem;
            font-weight: 700;
            color: #2c3e50;
            text-align: center;
            margin-bottom: 30px;
        }
        .metric-card {
            background-color: #f8f9fa;
            border-left: 4px solid var(--primary);
            padding: 20px;
            border-radius: 0 5px 5px 0;
            margin: 15px 0;
        }
        .metric-score {
            font-size: 1.5em;
            font-weight: bold;
            color: var(--primary);
        }
        .btn-primary {
            background-color: #6b5b95;
            border-color: #6b5b95;
            font-weight: 600;
            transition: background 0.3s ease;
        }
        .btn-primary:hover {
            background-color: #5a4b7a;
            border-color: #5a4b7a;
        }
        .feature-icon {
            color: var(--primary);
        }
        .results-container {
            display: none;
            margin-top: 30px;
        }
        .loading-spinner {
            display: none;
            text-align: center;
            margin: 20px 0;
        }
        .caption-display {
            background-color: #e8f7ff;
            border-left: 4px solid #3498db;
            padding: 20px;
            border-radius: 0 5px 5px 0;
            margin: 20px 0;
        }
        .reference-display {
            background-color: #f0f9ff;
            border-left: 4px solid #10b981;
            padding: 15px;
            border-radius: 0 5px 5px 0;
            margin: 10px 0;
        }
    </style>
</head>
<body>
    <!-- Navbar -->
    <nav class="navbar navbar-expand-lg navbar-light bg-white">
        <div class="container">
            <a class="navbar-brand fw-bold text-primary" href="{{ url_for('home') }}">IMGCaption AI</a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav"
                aria-controls="navbarNav" aria-expanded="false" aria-label="Toggle navigation">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav ms-auto">
                    <li class="nav-item"><a class="nav-link" href="{{ url_for('home') }}">Home</a></li>
                    <li class="nav-item"><a class="nav-link" href="{{ url_for('evaluate_page') }}">Evaluation</a></li>
                    <li class="nav-item"><a class="nav-link active" href="#">Auto-Evaluation</a></li>
                </ul>
            </div>
        </div>
    </nav>

    <div class="container">
        <div class="evaluation-section">
            <h1 class="evaluation-title">Automatic Caption Evaluation</h1>
            
            <div class="alert alert-info">
                <i class="fas fa-robot"></i>
                <strong>Automatic Evaluation:</strong> This page automatically evaluates the generated caption against ground truth references for image: <code>{{ filename }}</code>
            </div>

            <div class="row">
                <div class="col-md-12">
                    <h3><i class="fas fa-comment-alt feature-icon"></i> Generated Caption</h3>
                    <div class="caption-display">
                        <p><strong>Predicted Caption:</strong> {{ predicted_caption }}</p>
                    </div>
                    
                    {% if has_references %}
                    <h3><i class="fas fa-list feature-icon"></i> Reference Captions ({{ reference_captions|length }})</h3>
                    {% for ref_caption in reference_captions %}
                    <div class="reference-display">
                        <p><strong>Reference {{ loop.index }}:</strong> {{ ref_caption }}</p>
                    </div>
                    {% endfor %}
                    
                    <div class="text-center mt-4">
                        <button id="autoEvaluateBtn" class="btn btn-success btn-lg">
                            <i class="fas fa-chart-bar"></i> Run Automatic Evaluation
                        </button>
                    </div>
                    {% else %}
                    <div class="alert alert-warning">
                        <i class="fas fa-exclamation-triangle"></i>
                        <strong>No Reference Captions:</strong> No ground truth captions found for this image. Please add reference captions to the captions.txt file or use manual evaluation.
                    </div>
                    
                    <div class="text-center mt-4">
                        <a href="{{ url_for('evaluate_page') }}" class="btn btn-primary">
                            <i class="fas fa-calculator"></i> Use Manual Evaluation
                        </a>
                    </div>
                    {% endif %}
                    
                    <div class="loading-spinner" id="loadingSpinner">
                        <div class="spinner-border text-success" role="status">
                            <span class="visually-hidden">Evaluating...</span>
                        </div>
                        <p class="mt-2">Running automatic evaluation...</p>
                    </div>
                    
                    <div class="results-container" id="resultsContainer">
                        <h4><i class="fas fa-chart-line feature-icon"></i> Evaluation Results</h4>
                        <div id="evaluationResults"></div>
                        
                        <div class="text-center mt-4">
                            <a href="{{ url_for('home') }}" class="btn btn-primary me-2">
                                <i class="fas fa-arrow-left"></i> Try Another Image
                            </a>
                            <a href="{{ url_for('evaluate_page') }}" class="btn btn-secondary">
                                <i class="fas fa-calculator"></i> Manual Evaluation
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="text-center text-muted mt-4">
            <p>Automatic evaluation uses ground truth captions from the captions.txt file to assess model performance.</p>
        </div>
    </div>

    <!-- Footer -->
    <footer class="text-center py-4 mt-5 bg-white">
        <p class="mb-0">&copy; 2025 IMGCaption AI. All rights reserved.</p>
    </footer>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        {% if has_references %}
        document.getElementById('autoEvaluateBtn').addEventListener('click', async function() {
            // Show loading spinner
            document.getElementById('loadingSpinner').style.display = 'block';
            document.getElementById('resultsContainer').style.display = 'none';
            this.disabled = true;
            
            try {
                const response = await fetch('/evaluate_caption', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        predicted_caption: "{{ predicted_caption }}",
                        filename: "{{ filename }}"
                    })
                });
                
                const data = await response.json();
                
                if (data.success) {
                    displayResults(data.results);
                } else {
                    alert('Evaluation failed: ' + data.error);
                }
            } catch (error) {
                alert('Error during evaluation: ' + error.message);
            } finally {
                document.getElementById('loadingSpinner').style.display = 'none';
                this.disabled = false;
            }
        });

        function displayResults(results) {
            const container = document.getElementById('evaluationResults');
            container.innerHTML = '';
            
            // Group metrics
            const bleuMetrics = {};
            const otherMetrics = {};
            
            for (const [metric, score] of Object.entries(results)) {
                if (metric.startsWith('BLEU')) {
                    bleuMetrics[metric] = score;
                } else {
                    otherMetrics[metric] = score;
                }
            }
            
            // Display BLEU scores
            if (Object.keys(bleuMetrics).length > 0) {
                const bleuSection = document.createElement('div');
                bleuSection.innerHTML = '<h5>BLEU Scores</h5>';
                
                for (const [metric, score] of Object.entries(bleuMetrics)) {
                    const metricCard = document.createElement('div');
                    metricCard.className = 'metric-card';
                    metricCard.innerHTML = `
                        <div class="d-flex justify-content-between align-items-center">
                            <span><strong>${metric}</strong></span>
                            <span class="metric-score">${score.toFixed(4)}</span>
                        </div>
                    `;
                    bleuSection.appendChild(metricCard);
                }
                container.appendChild(bleuSection);
            }
            
            // Display other metrics
            if (Object.keys(otherMetrics).length > 0) {
                const otherSection = document.createElement('div');
                otherSection.innerHTML = '<h5 class="mt-4">Other Metrics</h5>';
                
                for (const [metric, score] of Object.entries(otherMetrics)) {
                    const metricCard = document.createElement('div');
                    metricCard.className = 'metric-card';
                    metricCard.innerHTML = `
                        <div class="d-flex justify-content-between align-items-center">
                            <span><strong>${metric}</strong></span>
                            <span class="metric-score">${score.toFixed(4)}</span>
                        </div>
                    `;
                    otherSection.appendChild(metricCard);
                }
                container.appendChild(otherSection);
            }
            
            document.getElementById('resultsContainer').style.display = 'block';
        }

        // Auto-run evaluation if this page was reached via auto-redirect
        if (window.location.search.includes('auto=true')) {
            setTimeout(() => {
                document.getElementById('autoEvaluateBtn').click();
            }, 1000);
        }
        {% endif %}
    </script>
</body>
</html>
