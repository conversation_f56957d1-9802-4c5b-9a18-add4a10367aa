<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title>Caption Results | Assistive AI</title>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;600;700&display=swap" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        :root {
            --primary: #6b5b95;
            --secondary: #b8a9c9;
            --light-bg: #f0f2f5;
            --dark-text: #333;
            --border-radius: 8px;
            --transition: 0.3s ease;
        }
        body {
            font-family: 'Inter', sans-serif;
            background: var(--light-bg);
            color: var(--dark-text);
        }
        .navbar {
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .result-section {
            background-color: #fff;
            border-radius: var(--border-radius);
            padding: 40px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .result-title {
            font-size: 2rem;
            font-weight: 700;
            color: #2c3e50;
            text-align: center;
            margin-bottom: 30px;
        }
        .caption-box, .ocr-box {
            background-color: #fff;
            border-left: 4px solid var(--primary);
            padding: 20px;
            border-radius: 0 5px 5px 0;
            margin: 20px 0;
        }
        .model-box {
            background-color: #e8f7ff;
            border-left: 4px solid #3498db;
            padding: 15px;
            border-radius: 0 5px 5px 0;
            margin: 20px 0;
        }
        .btn-primary {
            background-color: #6b5b95;
            border-color: #6b5b95;
            font-weight: 600;
            transition: background 0.3s ease;
        }
        .btn-primary:hover {
            background-color: #5a4b7a;
            border-color: #5a4b7a;
        }
        .audio-controls {
            text-align: center;
            margin-top: 20px;
        }
        .audio-controls audio {
            width: 100%;
            max-width: 400px;
            margin: 10px 0;
        }
        .audio-controls button {
            margin: 5px;
        }
        #audioStatus {
            font-style: italic;
            color: #666;
        }
        .image-container {
            text-align: center;
            margin: 20px 0;
            padding: 20px;
            background-color: #f8f9fa;
            border-radius: 8px;
            border: 2px dashed #dee2e6;
        }
        .image-container img {
            max-height: 450px;
            max-width: 100%;
            height: auto;
            border-radius: 8px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.15);
            border: 3px solid #fff;
        }
        .image-info {
            margin-top: 10px;
            font-size: 0.9em;
            color: #666;
        }
        .feature-icon {
            color: var(--primary);
        }
    </style>
</head>
<body>
    <!-- Navbar -->
    <nav class="navbar navbar-expand-lg navbar-light bg-white">
        <div class="container">
            <a class="navbar-brand fw-bold text-primary" href="{{ url_for('home') }}">IMGCaption AI</a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav"
                aria-controls="navbarNav" aria-expanded="false" aria-label="Toggle navigation">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav ms-auto">
                    <li class="nav-item"><a class="nav-link" href="{{ url_for('home') }}">Home</a></li>
                    <li class="nav-item"><a class="nav-link" href="#caption">Results</a></li>
                    <li class="nav-item"><a class="nav-link" href="{{ url_for('evaluate_page') }}">Evaluation</a></li>
                </ul>
            </div>
        </div>
    </nav>

    <div class="container">
        <div class="result-section">
            <h1 class="result-title">Image Analysis Results</h1>
            
            <div class="image-container">
                <h3 class="mb-3">
                    <i class="fas fa-image feature-icon"></i>
                    Uploaded Image
                </h3>
                <img src="{{ url_for('static', filename=image_path) }}"
                     alt="Uploaded Image"
                     class="img-fluid"
                     onerror="this.onerror=null; this.src='data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNDAwIiBoZWlnaHQ9IjMwMCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48cmVjdCB3aWR0aD0iMTAwJSIgaGVpZ2h0PSIxMDAlIiBmaWxsPSIjZGRkIi8+PHRleHQgeD0iNTAlIiB5PSI1MCUiIGZvbnQtZmFtaWx5PSJBcmlhbCwgc2Fucy1zZXJpZiIgZm9udC1zaXplPSIxOCIgZmlsbD0iIzk5OSIgdGV4dC1hbmNob3I9Im1pZGRsZSIgZHk9Ii4zZW0iPkltYWdlIG5vdCBmb3VuZDwvdGV4dD48L3N2Zz4='; this.parentElement.querySelector('.image-info').innerHTML = 'Error: Image file not found at path: {{ image_path }}';">
                <div class="image-info">
                    <small>Image path: {{ image_path }}</small>
                </div>
            </div>
            
            <div class="result-card">
                <div class="result-heading">
                    <i class="fas fa-brain feature-icon"></i>
                    <h3>Model Used</h3>
                </div>
                <div class="model-box">
                    <p>{{ model_used }}</p>
                </div>

                <div class="result-heading">
                    <i class="fas fa-comment-alt feature-icon"></i>
                    <h3>Generated Caption</h3>
                </div>
                <div class="caption-box">
                    <p>{{ caption }}</p>
                </div>
                
                {% if ocr_text %}
                <div class="result-heading">
                    <i class="fas fa-font feature-icon"></i>
                    <h3>Detected Text (OCR)</h3>
                </div>
                <div class="ocr-box">
                    <p>{{ ocr_text }}</p>
                </div>
                {% endif %}
                
                <div class="audio-controls">
                    <div class="result-heading justify-content-center">
                        <i class="fas fa-volume-up feature-icon"></i>
                        <h3>Audio Output</h3>
                    </div>
                    <audio id="captionAudio" controls class="mt-2" preload="auto">
                        <source src="{{ url_for('static', filename=audio_path) }}" type="audio/mpeg">
                        <source src="{{ url_for('static', filename=audio_path) }}" type="audio/mp3">
                        Your browser does not support the audio element.
                    </audio>
                    <div class="mt-2">
                        <button id="playAudioBtn" class="btn btn-primary btn-sm me-2">
                            <i class="fas fa-play"></i> Play Audio
                        </button>
                        <button id="pauseAudioBtn" class="btn btn-secondary btn-sm" style="display: none;">
                            <i class="fas fa-pause"></i> Pause
                        </button>
                    </div>
                    <p class="mt-2 text-muted">Click play to hear the caption read aloud</p>
                    <div id="audioStatus" class="mt-1 text-muted small"></div>
                </div>
            </div>
            
            <div class="text-center mt-4">
                {% if has_references %}
                <a href="{{ url_for('auto_evaluate', caption=caption, filename=filename) }}" class="btn btn-success me-2">
                    <i class="fas fa-chart-bar me-2"></i>Auto-Evaluate Caption
                </a>
                {% endif %}
                <a href="{{ url_for('evaluate_page') }}" class="btn btn-info me-2">
                    <i class="fas fa-calculator me-2"></i>Manual Evaluation
                </a>
                <a href="{{ url_for('home') }}" class="btn btn-primary">
                    <i class="fas fa-arrow-left me-2"></i>Try Another Image
                </a>
            </div>

            {% if has_references %}
            <div class="alert alert-success mt-3">
                <i class="fas fa-check-circle"></i>
                <strong>Auto-Evaluation Available:</strong> Ground truth captions found for this image.
                The system can automatically evaluate the generated caption against {{ reference_captions|length }} reference caption(s).
            </div>
            {% else %}
            <div class="alert alert-warning mt-3">
                <i class="fas fa-exclamation-triangle"></i>
                <strong>No Ground Truth:</strong> No reference captions found for "{{ filename }}".
                You can use manual evaluation or add reference captions to the captions.txt file.
            </div>
            {% endif %}
        </div>
        
        <div class="text-center text-muted mt-4">
            <p>This system uses AI to generate captions for images and convert them to speech, designed to assist visually impaired individuals.</p>
        </div>
    </div>

    <!-- Footer -->
    <footer class="text-center py-4 mt-5 bg-white">
        <p class="mb-0">&copy; 2025 IMGCaption AI. All rights reserved.</p>
    </footer>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const audioElement = document.getElementById('captionAudio');
            const playBtn = document.getElementById('playAudioBtn');
            const pauseBtn = document.getElementById('pauseAudioBtn');
            const statusDiv = document.getElementById('audioStatus');

            if (audioElement && playBtn && pauseBtn) {
                // Check if audio can be loaded
                audioElement.addEventListener('loadstart', function() {
                    statusDiv.textContent = 'Loading audio...';
                });

                audioElement.addEventListener('canplay', function() {
                    statusDiv.textContent = 'Audio ready to play';
                    playBtn.disabled = false;
                });

                audioElement.addEventListener('error', function(e) {
                    statusDiv.textContent = 'Error loading audio file';
                    console.error('Audio error:', e);
                });

                audioElement.addEventListener('play', function() {
                    playBtn.style.display = 'none';
                    pauseBtn.style.display = 'inline-block';
                    statusDiv.textContent = 'Playing...';
                });

                audioElement.addEventListener('pause', function() {
                    playBtn.style.display = 'inline-block';
                    pauseBtn.style.display = 'none';
                    statusDiv.textContent = 'Paused';
                });

                audioElement.addEventListener('ended', function() {
                    playBtn.style.display = 'inline-block';
                    pauseBtn.style.display = 'none';
                    statusDiv.textContent = 'Audio finished';

                    // Auto-redirect to evaluation if reference captions are available
                    {% if has_references %}
                    setTimeout(function() {
                        if (confirm('Audio playback finished. Would you like to automatically evaluate this caption against ground truth references?')) {
                            window.location.href = "{{ url_for('auto_evaluate', caption=caption, filename=filename) }}";
                        }
                    }, 2000); // Wait 2 seconds after audio ends
                    {% endif %}
                });

                // Play button click handler
                playBtn.addEventListener('click', function() {
                    audioElement.play().catch(e => {
                        console.error('Play failed:', e);
                        statusDiv.textContent = 'Failed to play audio';
                    });
                });

                // Pause button click handler
                pauseBtn.addEventListener('click', function() {
                    audioElement.pause();
                });

                // Try to auto-play after a delay (if browser allows)
                setTimeout(function() {
                    if (audioElement.readyState >= 2) { // HAVE_CURRENT_DATA
                        audioElement.play().catch(e => {
                            console.log('Auto-play prevented by browser:', e);
                            statusDiv.textContent = 'Click play button to hear audio';
                        });
                    }
                }, 1000);
            }
        });
    </script>
</body>
</html>