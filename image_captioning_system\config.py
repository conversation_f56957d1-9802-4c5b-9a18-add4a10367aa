"""
Configuration settings for the image captioning system.
"""

import os

# Path configurations
BASE_DIR = os.path.dirname(os.path.abspath(__file__))
FLICKR8K_DIR = os.path.join(BASE_DIR, "data", "flickr8k")
MSCOCO_DIR = os.path.join(BASE_DIR, "data", "mscoco")
PROCESSED_DATA_DIR = os.path.join(BASE_DIR, "data", "processed")
MODELS_DIR = os.path.join(BASE_DIR, "models", "saved_models")
CHECKPOINTS_DIR = os.path.join(BASE_DIR, "models", "checkpoints")

# Data configurations
FLICKR8K_IMAGES = os.path.join(FLICKR8K_DIR, "Images")
FLICKR8K_CAPTIONS = os.path.join(FLICKR8K_DIR, "captions.txt")
MSCOCO_TRAIN_IMAGES = os.path.join(MSCOCO_DIR, "train2017")
MSCOCO_VAL_IMAGES = os.path.join(MSCOCO_DIR, "val2017")
MSCOCO_TRAIN_ANNOTATIONS = os.path.join(
    MSCOCO_DIR, "annotations", "captions_train2017.json"
)
MSCOCO_VAL_ANNOTATIONS = os.path.join(
    MSCOCO_DIR, "annotations", "captions_val2017.json"
)

# Model configurations
BATCH_SIZE = 12
BUFFER_SIZE = 1000
EMBEDDING_DIM = 128
UNITS = 256
VOCAB_SIZE = 5000
MAX_LENGTH = 50
EPOCHS = 10
LEARNING_RATE = 0.0001
BEAM_SIZE = 3

# Regularization settings
DROPOUT_RATE = 0.7
L2_REGULARIZATION = 0.005
EARLY_STOPPING_PATIENCE = 5
MAX_TRAIN_SAMPLES = 144  # 12 batches × 12 samples per batch
MAX_VAL_SAMPLES = 144  # 12 batches × 12 samples per batch

# InceptionV3 settings
INCEPTION_IMAGE_SIZE = 299

# Attention configurations
ATTENTION_FEATURES_SHAPE = 64
ATTENTION_FEATURES_DIM = 2048

# OCR configurations
OCR_CONFIDENCE_THRESHOLD = 0.5

# Text-to-speech configurations
TTS_LANGUAGE = "en"
TTS_SLOW = False

# Evaluation configurations
BLEU_WEIGHTS = {
    "bleu-1": (1.0, 0.0, 0.0, 0.0),
    "bleu-2": (0.5, 0.5, 0.0, 0.0),
    "bleu-3": (0.33, 0.33, 0.33, 0.0),
    "bleu-4": (0.25, 0.25, 0.25, 0.25),
}

# Transformer configurations (Vision Transformer and GPT-2)
VIT_PRETRAINED_MODEL = "google/vit-base-patch16-224"
GPT2_PRETRAINED_MODEL = "gpt2"
FINE_TUNE_LAYERS = 3  # Number of layers to fine-tune in pretrained models

# Gemini API configurations
GEMINI_MODEL_NAME = "gemini-2.5-flash"  # Upgraded to Gemini 2.5 Flash (latest stable)
GEMINI_MAX_RETRIES = 3
GEMINI_RETRY_DELAY = 1.0
GEMINI_CACHE_SIZE = 100

# Model backend configuration
DEFAULT_BACKEND = "gemini"  # Options: "gemini", "local", "auto"
DEFAULT_MODEL_TYPE = "gru_lstm"  # For local models: "gru_lstm" or "transformer"

# Create directories if they don't exist
directories = [
    FLICKR8K_DIR,
    FLICKR8K_IMAGES,
    os.path.dirname(FLICKR8K_CAPTIONS),
    MSCOCO_DIR,
    MSCOCO_TRAIN_IMAGES,
    MSCOCO_VAL_IMAGES,
    os.path.dirname(MSCOCO_TRAIN_ANNOTATIONS),
    os.path.dirname(MSCOCO_VAL_ANNOTATIONS),
    PROCESSED_DATA_DIR,
    MODELS_DIR,
    CHECKPOINTS_DIR,
]
for directory in directories:
    os.makedirs(directory, exist_ok=True)

