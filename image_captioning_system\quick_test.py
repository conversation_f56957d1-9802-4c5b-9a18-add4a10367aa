#!/usr/bin/env python3
"""
Quick test to verify caption manager functionality.
"""

import os
import sys

# Add the current directory to the path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def main():
    print("Testing caption manager...")
    
    # Test the specific filename from the logs
    filename = "95151149_5ca6747df6.jpg"
    print(f"Testing filename: {filename}")
    
    try:
        from src.evaluation.caption_manager import GroundTruthCaptionManager
        
        manager = GroundTruthCaptionManager()
        print(f"Manager loaded {len(manager.captions_cache)} entries")
        
        captions = manager.get_reference_captions(filename)
        print(f"Found {len(captions)} captions for {filename}")
        
        if captions:
            print("Captions:")
            for i, caption in enumerate(captions, 1):
                print(f"  {i}: {caption}")
        else:
            print("No captions found")
            
            # Check if it exists in cache
            if filename in manager.captions_cache:
                print("ERROR: Filename exists in cache but get_reference_captions returned empty!")
            else:
                print("Filename not in cache")
                
                # Look for similar filenames
                similar = [k for k in manager.captions_cache.keys() if "95151149" in k]
                print(f"Similar filenames: {similar}")
        
        return len(captions) > 0
        
    except Exception as e:
        print(f"Error: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = main()
    print(f"Test {'PASSED' if success else 'FAILED'}")
