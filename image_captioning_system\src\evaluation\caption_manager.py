"""
Ground truth caption management system for automatic evaluation.
"""

import os
import sys
from typing import List, Dict, Optional, Tuple

# Add the root directory to sys.path
sys.path.append(os.path.abspath(os.path.join(os.path.dirname(__file__), "..", "..")))


class GroundTruthCaptionManager:
    """
    Manages ground truth captions for automatic evaluation.
    """
    
    def __init__(self, captions_file_path: str = None):
        """
        Initialize the caption manager.
        
        Args:
            captions_file_path: Path to the captions.txt file
        """
        if captions_file_path is None:
            # Default path relative to project root
            project_root = os.path.abspath(os.path.join(os.path.dirname(__file__), "..", ".."))
            captions_file_path = os.path.join(project_root, "data", "flickr8k", "captions.txt")
        
        self.captions_file_path = captions_file_path
        self.captions_cache = {}
        self.load_captions()
    
    def load_captions(self) -> None:
        """
        Load captions from the captions.txt file.
        """
        self.captions_cache = {}
        
        if not os.path.exists(self.captions_file_path):
            print(f"Warning: Captions file not found at {self.captions_file_path}")
            return
        
        try:
            with open(self.captions_file_path, 'r', encoding='utf-8') as f:
                for line_num, line in enumerate(f, 1):
                    line = line.strip()
                    
                    # Skip empty lines and comments
                    if not line or line.startswith('#'):
                        continue
                    
                    # Parse the line: Support both formats
                    # Format 1: filename|caption1|caption2|... (pipe-separated)
                    # Format 2: filename#number<tab>caption (Flickr8k format)

                    if '|' in line:
                        # Pipe-separated format
                        parts = line.split('|')
                        if len(parts) < 2:
                            print(f"Warning: Invalid format on line {line_num}: {line}")
                            continue

                        filename = parts[0].strip()
                        captions = [caption.strip() for caption in parts[1:] if caption.strip()]

                        if filename and captions:
                            self.captions_cache[filename] = captions

                    elif '\t' in line:
                        # Tab-separated format (Flickr8k style: filename#number<tab>caption)
                        parts = line.split('\t', 1)  # Split on first tab only
                        if len(parts) != 2:
                            print(f"Warning: Invalid format on line {line_num}: {line}")
                            continue

                        filename_with_number = parts[0].strip()
                        caption = parts[1].strip()

                        # Extract filename without the #number suffix
                        if '#' in filename_with_number:
                            filename = filename_with_number.split('#')[0]
                        else:
                            filename = filename_with_number

                        if filename and caption:
                            # Add to existing captions or create new entry
                            if filename in self.captions_cache:
                                self.captions_cache[filename].append(caption)
                            else:
                                self.captions_cache[filename] = [caption]

                    else:
                        print(f"Warning: Invalid format on line {line_num}: {line}")
                        continue
            
            print(f"Loaded {len(self.captions_cache)} ground truth caption entries")
            
        except Exception as e:
            print(f"Error loading captions file: {e}")
    
    def get_reference_captions(self, filename: str) -> List[str]:
        """
        Get reference captions for a given filename.

        Args:
            filename: The image filename (with or without path)

        Returns:
            List of reference captions, empty if not found
        """
        # Extract just the filename without path
        base_filename = os.path.basename(filename)

        # Try exact match first
        if base_filename in self.captions_cache:
            return self.captions_cache[base_filename]

        # Try case-insensitive match
        for cached_filename, captions in self.captions_cache.items():
            if cached_filename.lower() == base_filename.lower():
                return captions

        # Try without extension
        name_without_ext = os.path.splitext(base_filename)[0]
        for cached_filename, captions in self.captions_cache.items():
            cached_name_without_ext = os.path.splitext(cached_filename)[0]
            if cached_name_without_ext.lower() == name_without_ext.lower():
                return captions

        return []
    
    def has_reference_captions(self, filename: str) -> bool:
        """
        Check if reference captions exist for a given filename.
        
        Args:
            filename: The image filename
            
        Returns:
            True if reference captions exist, False otherwise
        """
        return len(self.get_reference_captions(filename)) > 0
    
    def add_caption_entry(self, filename: str, captions: List[str]) -> None:
        """
        Add a new caption entry to the cache and file.
        
        Args:
            filename: The image filename
            captions: List of reference captions
        """
        if not filename or not captions:
            return
        
        # Add to cache
        self.captions_cache[filename] = captions
        
        # Append to file
        try:
            with open(self.captions_file_path, 'a', encoding='utf-8') as f:
                caption_line = filename + '|' + '|'.join(captions) + '\n'
                f.write(caption_line)
            print(f"Added caption entry for {filename}")
        except Exception as e:
            print(f"Error adding caption entry: {e}")
    
    def get_all_filenames(self) -> List[str]:
        """
        Get all filenames that have reference captions.
        
        Returns:
            List of filenames
        """
        return list(self.captions_cache.keys())
    
    def get_statistics(self) -> Dict[str, float]:
        """
        Get statistics about the loaded captions.
        
        Returns:
            Dictionary with statistics
        """
        total_files = len(self.captions_cache)
        total_captions = sum(len(captions) for captions in self.captions_cache.values())
        avg_captions_per_file = total_captions / total_files if total_files > 0 else 0
        
        return {
            'total_files': total_files,
            'total_captions': total_captions,
            'avg_captions_per_file': round(avg_captions_per_file, 2)
        }
    
    def reload_captions(self) -> None:
        """
        Reload captions from the file.
        """
        self.load_captions()
    
    def create_sample_captions_file(self) -> None:
        """
        Create a sample captions.txt file with instructions.
        """
        sample_content = """# Ground Truth Captions File
# Format: filename|caption1|caption2|caption3|...
# Each line contains an image filename followed by one or more reference captions separated by pipes (|)
# Lines starting with # are comments and will be ignored
# 
# Example entries:
# sample_image.jpg|A person riding a bicycle|Someone cycling down the street|A cyclist on a bike
# another_image.png|A dog playing in the park|A canine running on grass|A pet enjoying outdoor time
#
# You can add multiple reference captions for each image to improve evaluation accuracy
# The system will automatically match uploaded image filenames to these reference captions

# Add your image captions below this line:
# your_image.jpg|Your reference caption here|Alternative caption|Another reference caption
"""
        
        try:
            os.makedirs(os.path.dirname(self.captions_file_path), exist_ok=True)
            with open(self.captions_file_path, 'w', encoding='utf-8') as f:
                f.write(sample_content)
            print(f"Created sample captions file at {self.captions_file_path}")
        except Exception as e:
            print(f"Error creating sample captions file: {e}")


# Global instance for use throughout the application
caption_manager = GroundTruthCaptionManager()


def get_reference_captions_for_image(filename: str) -> List[str]:
    """
    Convenience function to get reference captions for an image.
    
    Args:
        filename: The image filename
        
    Returns:
        List of reference captions
    """
    return caption_manager.get_reference_captions(filename)


def has_reference_captions_for_image(filename: str) -> bool:
    """
    Convenience function to check if reference captions exist for an image.
    
    Args:
        filename: The image filename
        
    Returns:
        True if reference captions exist
    """
    return caption_manager.has_reference_captions(filename)


# Example usage and testing
if __name__ == "__main__":
    manager = GroundTruthCaptionManager()
    
    # Test with a sample filename
    test_filename = "sample_image.jpg"
    captions = manager.get_reference_captions(test_filename)
    
    print(f"Reference captions for {test_filename}: {captions}")
    print(f"Statistics: {manager.get_statistics()}")
    print(f"All filenames: {manager.get_all_filenames()}")
