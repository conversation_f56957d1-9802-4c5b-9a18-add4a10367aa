"""
Comprehensive evaluation metrics for image captioning models.
Includes BLEU, METEOR, ROUGE-L, CIDEr, and SPICE scores.
"""

import os
import sys
import json
import tempfile
from typing import List, Dict, Any, <PERSON><PERSON>
import numpy as np

# Add the root directory to sys.path
sys.path.append(os.path.abspath(os.path.join(os.path.dirname(__file__), "..", "..")))

try:
    from nltk.translate.bleu_score import sentence_bleu, SmoothingFunction
    from nltk.translate.meteor_score import meteor_score
    from nltk.tokenize import word_tokenize
    import nltk
    
    # Download required NLTK data
    try:
        nltk.data.find('tokenizers/punkt')
    except LookupError:
        nltk.download('punkt')
    
    try:
        nltk.data.find('corpora/wordnet')
    except LookupError:
        nltk.download('wordnet')
        
except ImportError:
    print("Warning: NLTK not available. Some metrics will be unavailable.")
    sentence_bleu = None
    meteor_score = None
    word_tokenize = None

try:
    from rouge_score import rouge_scorer
except ImportError:
    print("Warning: rouge-score not available. ROUGE-L will be unavailable.")
    rouge_scorer = None

try:
    from pycocoevalcap.cider.cider import Cider
    from pycocoevalcap.spice.spice import Spice
    PYCOCOEVALCAP_AVAILABLE = True
except ImportError:
    print("Warning: pycocoevalcap not available. Using fallback implementations for CIDEr and SPICE.")
    Cider = None
    Spice = None
    PYCOCOEVALCAP_AVAILABLE = False


class ImageCaptioningEvaluator:
    """
    Comprehensive evaluator for image captioning models.
    """
    
    def __init__(self):
        """Initialize the evaluator."""
        self.smoothing_function = SmoothingFunction().method1 if sentence_bleu else None
        self.rouge_scorer = rouge_scorer.RougeScorer(['rougeL'], use_stemmer=True) if rouge_scorer else None
        
    def evaluate_single_caption(
        self, 
        predicted_caption: str, 
        reference_captions: List[str]
    ) -> Dict[str, float]:
        """
        Evaluate a single predicted caption against reference captions.
        
        Args:
            predicted_caption: The predicted caption
            reference_captions: List of reference captions
            
        Returns:
            Dictionary containing all evaluation metrics
        """
        results = {}
        
        # Tokenize captions
        if word_tokenize:
            pred_tokens = word_tokenize(predicted_caption.lower())
            ref_tokens_list = [word_tokenize(ref.lower()) for ref in reference_captions]
        else:
            pred_tokens = predicted_caption.lower().split()
            ref_tokens_list = [ref.lower().split() for ref in reference_captions]
        
        # BLEU scores
        if sentence_bleu:
            results.update(self._calculate_bleu_scores(pred_tokens, ref_tokens_list))
        
        # METEOR score
        if meteor_score:
            results['METEOR'] = self._calculate_meteor_score(predicted_caption, reference_captions)
        
        # ROUGE-L score
        if self.rouge_scorer:
            results['ROUGE-L'] = self._calculate_rouge_l_score(predicted_caption, reference_captions)
        
        # CIDEr and SPICE (require special format)
        if PYCOCOEVALCAP_AVAILABLE and (Cider or Spice):
            cider_spice_results = self._calculate_cider_spice_scores(
                predicted_caption, reference_captions
            )
            results.update(cider_spice_results)
        else:
            # Use fallback implementations
            fallback_results = self._calculate_fallback_cider_spice(
                predicted_caption, reference_captions
            )
            results.update(fallback_results)
        
        return results
    
    def _calculate_bleu_scores(self, pred_tokens: List[str], ref_tokens_list: List[List[str]]) -> Dict[str, float]:
        """Calculate BLEU-1, BLEU-2, BLEU-3, BLEU-4 scores."""
        bleu_scores = {}
        
        weights_list = [
            (1.0, 0.0, 0.0, 0.0),  # BLEU-1
            (0.5, 0.5, 0.0, 0.0),  # BLEU-2
            (0.33, 0.33, 0.33, 0.0),  # BLEU-3
            (0.25, 0.25, 0.25, 0.25)  # BLEU-4
        ]
        
        for i, weights in enumerate(weights_list, 1):
            try:
                score = sentence_bleu(
                    ref_tokens_list, 
                    pred_tokens, 
                    weights=weights,
                    smoothing_function=self.smoothing_function
                )
                bleu_scores[f'BLEU-{i}'] = score
            except Exception as e:
                print(f"Error calculating BLEU-{i}: {e}")
                bleu_scores[f'BLEU-{i}'] = 0.0
        
        return bleu_scores
    
    def _calculate_meteor_score(self, predicted: str, references: List[str]) -> float:
        """Calculate METEOR score."""
        try:
            # METEOR score with multiple references
            # METEOR expects tokenized input (list of words)
            if word_tokenize:
                predicted_tokens = word_tokenize(predicted.lower())
                scores = []
                for ref in references:
                    ref_tokens = word_tokenize(ref.lower())
                    score = meteor_score([ref_tokens], predicted_tokens)
                    scores.append(score)
                return max(scores)  # Take the best score among references
            else:
                # Fallback to simple word splitting if nltk not available
                predicted_tokens = predicted.lower().split()
                scores = []
                for ref in references:
                    ref_tokens = ref.lower().split()
                    # Simple word overlap score as fallback
                    pred_set = set(predicted_tokens)
                    ref_set = set(ref_tokens)
                    if len(pred_set) == 0 and len(ref_set) == 0:
                        score = 1.0
                    elif len(pred_set) == 0 or len(ref_set) == 0:
                        score = 0.0
                    else:
                        score = len(pred_set.intersection(ref_set)) / len(pred_set.union(ref_set))
                    scores.append(score)
                return max(scores)
        except Exception as e:
            print(f"Error calculating METEOR: {e}")
            return 0.0
    
    def _calculate_rouge_l_score(self, predicted: str, references: List[str]) -> float:
        """Calculate ROUGE-L score."""
        try:
            scores = []
            for ref in references:
                score = self.rouge_scorer.score(ref, predicted)
                scores.append(score['rougeL'].fmeasure)
            return max(scores)  # Take the best score among references
        except Exception as e:
            print(f"Error calculating ROUGE-L: {e}")
            return 0.0
    
    def _calculate_cider_spice_scores(self, predicted: str, references: List[str]) -> Dict[str, float]:
        """Calculate CIDEr and SPICE scores."""
        results = {}
        
        # Format data for pycocoevalcap
        gts = {'image_0': references}
        res = {'image_0': [predicted]}
        
        # CIDEr score
        if Cider:
            try:
                cider_scorer = Cider()
                cider_score, _ = cider_scorer.compute_score(gts, res)
                results['CIDEr'] = cider_score
            except Exception as e:
                print(f"Error calculating CIDEr: {e}")
                results['CIDEr'] = 0.0
        
        # SPICE score
        if Spice:
            try:
                spice_scorer = Spice()
                spice_score, _ = spice_scorer.compute_score(gts, res)
                results['SPICE'] = spice_score
            except Exception as e:
                print(f"Error calculating SPICE: {e}")
                results['SPICE'] = 0.0
        
        return results

    def _calculate_fallback_cider_spice(self, predicted: str, references: List[str]) -> Dict[str, float]:
        """
        Fallback implementations for CIDEr and SPICE when pycocoevalcap is not available.
        These provide reasonable approximations using simpler methods.
        """
        results = {}

        # Fallback CIDEr: TF-IDF weighted n-gram similarity
        try:
            cider_score = self._fallback_cider(predicted, references)
            results['CIDEr'] = cider_score
        except Exception as e:
            print(f"Error calculating fallback CIDEr: {e}")
            results['CIDEr'] = 0.0

        # Fallback SPICE: Enhanced semantic word overlap
        try:
            spice_score = self._fallback_spice(predicted, references)
            results['SPICE'] = spice_score
        except Exception as e:
            print(f"Error calculating fallback SPICE: {e}")
            results['SPICE'] = 0.0

        return results

    def _fallback_cider(self, predicted: str, references: List[str]) -> float:
        """
        Fallback CIDEr implementation using TF-IDF weighted n-gram similarity.
        """
        import math
        from collections import Counter, defaultdict

        def get_ngrams(text: str, n: int) -> List[str]:
            """Get n-grams from text."""
            if word_tokenize:
                tokens = word_tokenize(text.lower())
            else:
                tokens = text.lower().split()
            return [' '.join(tokens[i:i+n]) for i in range(len(tokens)-n+1)]

        def compute_tf_idf(ngrams_list: List[List[str]]) -> Dict[str, float]:
            """Compute TF-IDF scores for n-grams."""
            # Document frequency
            df = defaultdict(int)
            total_docs = len(ngrams_list)

            for ngrams in ngrams_list:
                unique_ngrams = set(ngrams)
                for ngram in unique_ngrams:
                    df[ngram] += 1

            # TF-IDF scores
            tf_idf = {}
            for ngrams in ngrams_list:
                ngram_counts = Counter(ngrams)
                doc_tf_idf = {}
                for ngram, count in ngram_counts.items():
                    tf = count / len(ngrams) if len(ngrams) > 0 else 0
                    idf = math.log(total_docs / df[ngram]) if df[ngram] > 0 else 0
                    doc_tf_idf[ngram] = tf * idf
                tf_idf[len(tf_idf)] = doc_tf_idf

            return tf_idf

        # Get n-grams for different n values
        all_ngrams = []
        pred_ngrams_dict = {}
        ref_ngrams_dict = {}

        for n in range(1, 5):  # 1-grams to 4-grams
            pred_ngrams = get_ngrams(predicted, n)
            ref_ngrams_list = [get_ngrams(ref, n) for ref in references]

            pred_ngrams_dict[n] = pred_ngrams
            ref_ngrams_dict[n] = ref_ngrams_list

            all_ngrams.extend([pred_ngrams] + ref_ngrams_list)

        # Compute TF-IDF
        tf_idf_scores = compute_tf_idf(all_ngrams)

        # Calculate CIDEr-like score
        scores = []
        for n in range(1, 5):
            pred_ngrams = pred_ngrams_dict[n]
            ref_ngrams_list = ref_ngrams_dict[n]

            pred_counter = Counter(pred_ngrams)

            n_scores = []
            for ref_ngrams in ref_ngrams_list:
                ref_counter = Counter(ref_ngrams)

                # Compute cosine similarity with TF-IDF weighting
                numerator = 0
                pred_norm = 0
                ref_norm = 0

                all_ngrams_set = set(pred_counter.keys()) | set(ref_counter.keys())

                for ngram in all_ngrams_set:
                    pred_count = pred_counter.get(ngram, 0)
                    ref_count = ref_counter.get(ngram, 0)

                    # Simple TF-IDF approximation
                    weight = 1.0  # Simplified weighting

                    numerator += pred_count * ref_count * weight
                    pred_norm += (pred_count * weight) ** 2
                    ref_norm += (ref_count * weight) ** 2

                if pred_norm > 0 and ref_norm > 0:
                    similarity = numerator / (math.sqrt(pred_norm) * math.sqrt(ref_norm))
                    n_scores.append(similarity)
                else:
                    n_scores.append(0.0)

            if n_scores:
                scores.append(max(n_scores))  # Take best score among references

        # Average across n-gram orders
        return sum(scores) / len(scores) if scores else 0.0

    def _fallback_spice(self, predicted: str, references: List[str]) -> float:
        """
        Fallback SPICE implementation using enhanced semantic word overlap.
        """
        def get_content_words(text: str) -> set:
            """Extract content words (nouns, verbs, adjectives)."""
            if word_tokenize:
                tokens = word_tokenize(text.lower())
            else:
                tokens = text.lower().split()

            # Simple content word filtering (remove common stop words)
            stop_words = {'the', 'a', 'an', 'and', 'or', 'but', 'in', 'on', 'at', 'to', 'for', 'of', 'with', 'by', 'is', 'are', 'was', 'were', 'be', 'been', 'being', 'have', 'has', 'had', 'do', 'does', 'did', 'will', 'would', 'could', 'should', 'may', 'might', 'must', 'can', 'this', 'that', 'these', 'those'}

            content_words = set()
            for token in tokens:
                if len(token) > 2 and token not in stop_words and token.isalpha():
                    content_words.add(token)

            return content_words

        def semantic_similarity(word1: str, word2: str) -> float:
            """Simple semantic similarity based on string similarity."""
            if word1 == word2:
                return 1.0

            # Simple edit distance-based similarity
            def edit_distance(s1: str, s2: str) -> int:
                if len(s1) < len(s2):
                    return edit_distance(s2, s1)

                if len(s2) == 0:
                    return len(s1)

                previous_row = list(range(len(s2) + 1))
                for i, c1 in enumerate(s1):
                    current_row = [i + 1]
                    for j, c2 in enumerate(s2):
                        insertions = previous_row[j + 1] + 1
                        deletions = current_row[j] + 1
                        substitutions = previous_row[j] + (c1 != c2)
                        current_row.append(min(insertions, deletions, substitutions))
                    previous_row = current_row

                return previous_row[-1]

            max_len = max(len(word1), len(word2))
            if max_len == 0:
                return 1.0

            distance = edit_distance(word1, word2)
            similarity = 1.0 - (distance / max_len)

            # Only consider as similar if similarity is high enough
            return similarity if similarity > 0.7 else 0.0

        # Extract content words
        pred_words = get_content_words(predicted)

        # Calculate semantic overlap with each reference
        scores = []
        for reference in references:
            ref_words = get_content_words(reference)

            if len(pred_words) == 0 and len(ref_words) == 0:
                scores.append(1.0)
                continue
            elif len(pred_words) == 0 or len(ref_words) == 0:
                scores.append(0.0)
                continue

            # Calculate semantic overlap
            matched_pred = set()
            matched_ref = set()

            for pred_word in pred_words:
                for ref_word in ref_words:
                    if semantic_similarity(pred_word, ref_word) > 0.7:
                        matched_pred.add(pred_word)
                        matched_ref.add(ref_word)
                        break

            # F1-like score
            precision = len(matched_pred) / len(pred_words) if len(pred_words) > 0 else 0
            recall = len(matched_ref) / len(ref_words) if len(ref_words) > 0 else 0

            if precision + recall > 0:
                f1 = 2 * precision * recall / (precision + recall)
                scores.append(f1)
            else:
                scores.append(0.0)

        return max(scores) if scores else 0.0

    def evaluate_batch(
        self, 
        predictions: List[str], 
        references: List[List[str]]
    ) -> Dict[str, float]:
        """
        Evaluate a batch of predictions against references.
        
        Args:
            predictions: List of predicted captions
            references: List of reference caption lists (one list per prediction)
            
        Returns:
            Dictionary containing averaged evaluation metrics
        """
        if len(predictions) != len(references):
            raise ValueError("Number of predictions must match number of reference lists")
        
        all_scores = []
        
        for pred, refs in zip(predictions, references):
            scores = self.evaluate_single_caption(pred, refs)
            all_scores.append(scores)
        
        # Average all scores
        averaged_scores = {}
        if all_scores:
            for metric in all_scores[0].keys():
                scores = [score_dict.get(metric, 0.0) for score_dict in all_scores]
                averaged_scores[metric] = np.mean(scores)
        
        return averaged_scores
    
    def get_available_metrics(self) -> List[str]:
        """Get list of available metrics based on installed packages."""
        metrics = []
        
        if sentence_bleu:
            metrics.extend(['BLEU-1', 'BLEU-2', 'BLEU-3', 'BLEU-4'])
        
        if meteor_score:
            metrics.append('METEOR')
        
        if self.rouge_scorer:
            metrics.append('ROUGE-L')
        
        # CIDEr and SPICE are always available (with fallback implementations)
        metrics.append('CIDEr')
        metrics.append('SPICE')
        
        return metrics


def format_evaluation_results(results: Dict[str, float]) -> str:
    """
    Format evaluation results for display.
    
    Args:
        results: Dictionary of metric scores
        
    Returns:
        Formatted string representation
    """
    if not results:
        return "No evaluation results available."
    
    formatted = "Evaluation Results:\n"
    formatted += "=" * 50 + "\n"
    
    # Group metrics
    bleu_metrics = {k: v for k, v in results.items() if k.startswith('BLEU')}
    other_metrics = {k: v for k, v in results.items() if not k.startswith('BLEU')}
    
    # Display BLEU scores
    if bleu_metrics:
        formatted += "BLEU Scores:\n"
        for metric, score in sorted(bleu_metrics.items()):
            formatted += f"  {metric}: {score:.4f}\n"
        formatted += "\n"
    
    # Display other metrics
    if other_metrics:
        formatted += "Other Metrics:\n"
        for metric, score in sorted(other_metrics.items()):
            formatted += f"  {metric}: {score:.4f}\n"
    
    return formatted


# Example usage and testing
if __name__ == "__main__":
    evaluator = ImageCaptioningEvaluator()
    
    # Test with sample data
    predicted = "a man is riding a bike"
    references = [
        "a person is riding a bicycle",
        "a man rides his bike",
        "someone is cycling"
    ]
    
    print("Available metrics:", evaluator.get_available_metrics())
    
    results = evaluator.evaluate_single_caption(predicted, references)
    print(format_evaluation_results(results))
