"""
Gemini API adapter for image captioning.
This module provides a unified interface to use Google's Gemini API
while maintaining compatibility with the existing model architecture.
"""

import base64
import io
import os
import sys
import time
from typing import Optional, Tuple, List, Dict, Any

import google.generativeai as genai
import numpy as np
from PIL import Image
import requests

sys.path.append(os.path.abspath(os.path.join(os.path.dirname(__file__), "..", "..")))
from config import MAX_LENGTH, BEAM_SIZE


class GeminiImageCaptioner:
    """
    Gemini API adapter for image captioning that maintains compatibility
    with the existing model interface.
    """

    def __init__(
        self,
        api_key: str,
        model_name: str = "gemini-2.5-flash",  # Upgraded from gemini-2.0-flash-exp
        max_retries: int = 3,
        retry_delay: float = 1.0,
    ):
        """
        Initialize Gemini image captioner.

        Args:
            api_key: Google API key for Gemini
            model_name: Gemini model name to use
            max_retries: Maximum number of API call retries
            retry_delay: Delay between retries in seconds
        """
        self.api_key = api_key
        self.model_name = model_name
        self.max_retries = max_retries
        self.retry_delay = retry_delay

        # Configure Gemini API
        genai.configure(api_key=api_key)
        self.model = genai.GenerativeModel(model_name)

        # Cache for storing recent results to avoid redundant API calls
        self.cache = {}
        self.cache_size = 100

    def _prepare_image(self, image_path: str) -> Image.Image:
        """
        Prepare image for Gemini API.

        Args:
            image_path: Path to the image file

        Returns:
            PIL Image object
        """
        try:
            if isinstance(image_path, str):
                # Load from file path
                image = Image.open(image_path)
            else:
                # Assume it's already a PIL Image or numpy array
                if isinstance(image_path, np.ndarray):
                    image = Image.fromarray(image_path)
                else:
                    image = image_path

            # Convert to RGB if necessary
            if image.mode != 'RGB':
                image = image.convert('RGB')

            # Resize if too large (Gemini has size limits)
            max_size = 1024
            if max(image.size) > max_size:
                image.thumbnail((max_size, max_size), Image.Resampling.LANCZOS)

            return image

        except Exception as e:
            raise ValueError(f"Error preparing image: {e}")

    def _create_cache_key(self, image_path: str, prompt: str) -> str:
        """
        Create a cache key for the image and prompt combination.

        Args:
            image_path: Path to the image
            prompt: Text prompt

        Returns:
            Cache key string
        """
        # Simple cache key based on image path and prompt
        return f"{image_path}_{hash(prompt)}"

    def _call_gemini_api(self, image: Image.Image, prompt: str) -> str:
        """
        Make API call to Gemini with retry logic.

        Args:
            image: PIL Image object
            prompt: Text prompt for captioning

        Returns:
            Generated caption text
        """
        for attempt in range(self.max_retries):
            try:
                # Generate content using Gemini
                response = self.model.generate_content([prompt, image])
                
                if response.text:
                    return response.text.strip()
                else:
                    raise ValueError("Empty response from Gemini API")

            except Exception as e:
                if attempt < self.max_retries - 1:
                    print(f"Gemini API call failed (attempt {attempt + 1}): {e}")
                    time.sleep(self.retry_delay * (2 ** attempt))  # Exponential backoff
                else:
                    raise Exception(f"Gemini API call failed after {self.max_retries} attempts: {e}")

    def generate_caption(
        self,
        image_path: str,
        style: str = "descriptive",
        include_objects: bool = True,
        include_scene: bool = True,
        include_actions: bool = True,
        max_length: int = MAX_LENGTH,
    ) -> str:
        """
        Generate caption for an image using Gemini API.

        Args:
            image_path: Path to the image file
            style: Caption style ('descriptive', 'concise', 'detailed')
            include_objects: Whether to include object descriptions
            include_scene: Whether to include scene description
            include_actions: Whether to include action descriptions
            max_length: Maximum caption length

        Returns:
            Generated caption string
        """
        # Create prompt based on parameters
        prompt = self._create_prompt(
            style=style,
            include_objects=include_objects,
            include_scene=include_scene,
            include_actions=include_actions,
            max_length=max_length,
        )

        # Check cache first
        cache_key = self._create_cache_key(str(image_path), prompt)
        if cache_key in self.cache:
            return self.cache[cache_key]

        # Prepare image
        image = self._prepare_image(image_path)

        # Call Gemini API
        caption = self._call_gemini_api(image, prompt)

        # Post-process caption
        caption = self._post_process_caption(caption, max_length)

        # Cache result
        if len(self.cache) >= self.cache_size:
            # Remove oldest entry
            oldest_key = next(iter(self.cache))
            del self.cache[oldest_key]
        self.cache[cache_key] = caption

        return caption

    def _create_prompt(
        self,
        style: str = "descriptive",
        include_objects: bool = True,
        include_scene: bool = True,
        include_actions: bool = True,
        max_length: int = MAX_LENGTH,
    ) -> str:
        """
        Create a prompt for Gemini based on the specified parameters.

        Args:
            style: Caption style
            include_objects: Whether to include objects
            include_scene: Whether to include scene
            include_actions: Whether to include actions
            max_length: Maximum caption length

        Returns:
            Formatted prompt string
        """
        base_prompt = "Generate a caption for this image"

        # Style specifications
        style_specs = {
            "descriptive": "that is descriptive and informative",
            "concise": "that is brief and to the point",
            "detailed": "that is comprehensive and detailed",
        }

        prompt_parts = [base_prompt, style_specs.get(style, style_specs["descriptive"])]

        # Add specific requirements
        requirements = []
        if include_objects:
            requirements.append("describe the main objects and people")
        if include_scene:
            requirements.append("describe the setting and environment")
        if include_actions:
            requirements.append("describe any actions or activities")

        if requirements:
            prompt_parts.append(f"Make sure to {', '.join(requirements)}")

        # Add length constraint
        word_limit = max_length // 2  # Rough estimate of words from character limit
        prompt_parts.append(f"Keep the caption under {word_limit} words")

        # Add accessibility focus
        prompt_parts.append(
            "This caption will be used to help visually impaired individuals understand the image, "
            "so be clear and specific about visual elements"
        )

        return ". ".join(prompt_parts) + "."

    def _post_process_caption(self, caption: str, max_length: int) -> str:
        """
        Post-process the generated caption.

        Args:
            caption: Raw caption from Gemini
            max_length: Maximum allowed length

        Returns:
            Processed caption
        """
        # Remove any unwanted prefixes or suffixes
        caption = caption.strip()

        # Remove common prefixes that Gemini might add
        prefixes_to_remove = [
            "This image shows",
            "The image shows",
            "In this image",
            "The picture shows",
            "This picture shows",
            "Caption:",
            "Description:",
        ]

        for prefix in prefixes_to_remove:
            if caption.lower().startswith(prefix.lower()):
                caption = caption[len(prefix):].strip()
                break

        # Ensure it starts with a capital letter
        if caption and not caption[0].isupper():
            caption = caption[0].upper() + caption[1:]

        # Truncate if too long
        if len(caption) > max_length:
            # Try to cut at a sentence boundary
            sentences = caption.split('. ')
            truncated = ""
            for sentence in sentences:
                if len(truncated + sentence + '. ') <= max_length:
                    truncated += sentence + '. '
                else:
                    break
            
            if truncated:
                caption = truncated.rstrip('. ') + '.'
            else:
                # If no sentence boundary works, cut at word boundary
                words = caption.split()
                truncated_words = []
                char_count = 0
                for word in words:
                    if char_count + len(word) + 1 <= max_length:
                        truncated_words.append(word)
                        char_count += len(word) + 1
                    else:
                        break
                caption = ' '.join(truncated_words)
                if not caption.endswith('.'):
                    caption += '.'

        return caption

    def generate_multiple_captions(
        self,
        image_path: str,
        num_captions: int = 3,
        **kwargs
    ) -> List[str]:
        """
        Generate multiple captions for the same image (simulates beam search).

        Args:
            image_path: Path to the image
            num_captions: Number of captions to generate
            **kwargs: Additional arguments for generate_caption

        Returns:
            List of generated captions
        """
        captions = []
        
        # Generate captions with slight prompt variations
        styles = ["descriptive", "concise", "detailed"]
        
        for i in range(num_captions):
            style = styles[i % len(styles)]
            try:
                caption = self.generate_caption(
                    image_path,
                    style=style,
                    **kwargs
                )
                captions.append(caption)
            except Exception as e:
                print(f"Error generating caption {i+1}: {e}")
                # Fallback caption
                captions.append("Unable to generate caption for this image.")
        
        return captions

    def clear_cache(self):
        """Clear the caption cache."""
        self.cache.clear()
