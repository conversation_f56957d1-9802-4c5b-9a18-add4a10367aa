#!/usr/bin/env python3
"""
Test script to debug caption manager issues.
"""

import os
import sys

# Add the current directory to the path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_caption_manager():
    """Test the caption manager functionality."""
    print("=== Testing Caption Manager ===")
    
    try:
        print("1. Importing caption manager...")
        from src.evaluation.caption_manager import GroundTruthCaptionManager, get_reference_captions_for_image
        print("   ✓ Import successful")
        
        print("2. Creating caption manager instance...")
        manager = GroundTruthCaptionManager()
        print(f"   ✓ Manager created, loaded {len(manager.captions_cache)} entries")
        
        print("3. Testing specific filename...")
        filename = "816084977_21c1811c9a.jpg"
        print(f"   Looking for: '{filename}'")
        
        # Test direct method
        captions_direct = manager.get_reference_captions(filename)
        print(f"   Direct method: Found {len(captions_direct)} captions")
        
        # Test convenience function
        captions_func = get_reference_captions_for_image(filename)
        print(f"   Convenience function: Found {len(captions_func)} captions")
        
        if captions_direct:
            print("   First caption (direct):", captions_direct[0])
        
        if captions_func:
            print("   First caption (function):", captions_func[0])
        
        print("4. Testing cache contents...")
        print(f"   Total cache entries: {len(manager.captions_cache)}")
        print(f"   Sample cache keys: {list(manager.captions_cache.keys())[:5]}")
        
        # Check if the exact filename exists in cache
        if filename in manager.captions_cache:
            print(f"   ✓ Exact match found in cache")
            print(f"   Captions: {manager.captions_cache[filename]}")
        else:
            print(f"   ✗ Exact match NOT found in cache")
            
            # Check for similar filenames
            similar = [k for k in manager.captions_cache.keys() if "816084977" in k]
            if similar:
                print(f"   Similar filenames found: {similar}")
            else:
                print(f"   No similar filenames found")
        
        print("5. Testing file path...")
        print(f"   Caption file path: {manager.captions_file_path}")
        print(f"   File exists: {os.path.exists(manager.captions_file_path)}")
        
        return True
        
    except Exception as e:
        print(f"   ✗ Error: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_file_search():
    """Test searching for the filename in the file directly."""
    print("\n=== Testing File Search ===")
    
    try:
        caption_file = "data/flickr8k/captions.txt"
        if not os.path.exists(caption_file):
            print(f"   ✗ Caption file not found: {caption_file}")
            return False
        
        print(f"   Searching in: {caption_file}")
        filename = "816084977_21c1811c9a.jpg"
        
        found_lines = []
        with open(caption_file, 'r', encoding='utf-8') as f:
            for line_num, line in enumerate(f, 1):
                if filename in line:
                    found_lines.append((line_num, line.strip()))
        
        print(f"   Found {len(found_lines)} lines containing '{filename}':")
        for line_num, line in found_lines:
            print(f"     Line {line_num}: {line}")
        
        return len(found_lines) > 0
        
    except Exception as e:
        print(f"   ✗ Error: {e}")
        return False

if __name__ == "__main__":
    print("Caption Manager Debug Test")
    print("=" * 50)
    
    success1 = test_caption_manager()
    success2 = test_file_search()
    
    print("\n" + "=" * 50)
    if success1 and success2:
        print("✓ All tests passed")
    else:
        print("✗ Some tests failed")
