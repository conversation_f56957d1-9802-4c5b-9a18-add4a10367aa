#!/usr/bin/env python3
"""
Test script to verify the Gemini model upgrade from 2.0-flash-exp to 2.5-flash.
"""

import os
import sys
import time
from pathlib import Path

# Add the current directory to the path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_gemini_model_upgrade():
    """Test the upgraded Gemini model."""
    print("🧪 Testing Gemini Model Upgrade")
    print("=" * 50)
    
    try:
        from src.models.unified_captioner import UnifiedImageCaptioner
        from src.models.gemini_adapter import GeminiImageCaptioner
        
        # Check if API key is available
        api_key = os.getenv("GEMINI_API_KEY")
        if not api_key:
            print("❌ GEMINI_API_KEY not found in environment variables")
            print("Please set your API key: export GEMINI_API_KEY=your_key_here")
            return False
        
        print("✅ API key found")
        
        # Test 1: Direct Gemini adapter
        print("\n📋 Test 1: Direct Gemini Adapter")
        print("-" * 30)
        
        gemini_adapter = GeminiImageCaptioner(api_key=api_key)
        print(f"✅ Model initialized: {gemini_adapter.model_name}")
        
        # Test 2: Unified captioner
        print("\n📋 Test 2: Unified Captioner")
        print("-" * 30)
        
        captioner = UnifiedImageCaptioner(
            backend="gemini",
            gemini_api_key=api_key
        )
        
        backend_info = captioner.get_backend_info()
        print(f"✅ Backend: {backend_info['backend']}")
        print(f"✅ Model type: {backend_info['model_type']}")
        print(f"✅ Gemini available: {backend_info['gemini_available']}")
        
        # Test 3: Caption generation (if test image exists)
        print("\n📋 Test 3: Caption Generation")
        print("-" * 30)
        
        # Look for a test image
        test_image_paths = [
            "static/uploads",
            "data/flickr8k/Images",
            "test_images"
        ]
        
        test_image = None
        for path in test_image_paths:
            if os.path.exists(path):
                for file in os.listdir(path):
                    if file.lower().endswith(('.jpg', '.jpeg', '.png')):
                        test_image = os.path.join(path, file)
                        break
                if test_image:
                    break
        
        if test_image:
            print(f"📸 Testing with image: {test_image}")
            
            start_time = time.time()
            caption = captioner.generate_caption(test_image)
            end_time = time.time()
            
            print(f"✅ Caption generated in {end_time - start_time:.2f} seconds")
            print(f"📝 Caption: {caption}")
            
            # Test multiple captions
            print("\n🔄 Testing multiple captions...")
            captions = captioner.generate_multiple_captions(test_image, num_captions=3)
            for i, cap in enumerate(captions, 1):
                print(f"   {i}. {cap}")
                
        else:
            print("⚠️  No test images found. Skipping caption generation test.")
            print("   You can test manually by uploading an image through the web interface.")
        
        print("\n🎉 Model upgrade test completed successfully!")
        print(f"🚀 Now using: {gemini_adapter.model_name}")
        return True
        
    except Exception as e:
        print(f"❌ Error during testing: {e}")
        import traceback
        traceback.print_exc()
        return False

def compare_model_features():
    """Compare features between old and new models."""
    print("\n📊 Model Comparison")
    print("=" * 50)
    
    comparison = {
        "Feature": ["Model Version", "Status", "Thinking Capabilities", "Rate Limits", "Stability", "Performance"],
        "gemini-2.0-flash-exp": ["2.0", "Experimental", "Limited", "Restrictive", "May change", "Good"],
        "gemini-2.5-flash": ["2.5", "Stable", "Built-in", "Standard", "Stable", "Enhanced"]
    }
    
    # Print comparison table
    for i, feature in enumerate(comparison["Feature"]):
        old_val = comparison["gemini-2.0-flash-exp"][i]
        new_val = comparison["gemini-2.5-flash"][i]
        print(f"{feature:20} | {old_val:15} → {new_val}")
    
    print("\n🎯 Key Improvements:")
    print("   • Enhanced thinking and reasoning capabilities")
    print("   • Better semantic understanding of images")
    print("   • More stable and reliable performance")
    print("   • Higher rate limits for production use")
    print("   • Latest model with June 2025 updates")

def main():
    """Main test function."""
    print("🔧 Gemini Model Upgrade Test Suite")
    print("=" * 60)
    
    # Show current configuration
    from config import GEMINI_MODEL_NAME
    print(f"📋 Configured model: {GEMINI_MODEL_NAME}")
    
    # Run tests
    success = test_gemini_model_upgrade()
    
    # Show comparison
    compare_model_features()
    
    if success:
        print("\n✅ All tests passed! Model upgrade successful.")
        print("🚀 Your image captioning system is now using Gemini 2.5 Flash!")
    else:
        print("\n❌ Some tests failed. Please check the error messages above.")
    
    return success

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
