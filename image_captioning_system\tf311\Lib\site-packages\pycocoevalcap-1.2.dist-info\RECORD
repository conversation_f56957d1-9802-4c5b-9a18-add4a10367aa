pycocoevalcap-1.2.dist-info/INSTALLER,sha256=zuuue4knoyJ-UwPPXg8fezS7VCrXJQrAP7zeNuwvFQg,4
pycocoevalcap-1.2.dist-info/METADATA,sha256=wdTy4zE1ZqYS2ajDEYVmIe9ecR2C-2_3D2srz5gk9J0,3171
pycocoevalcap-1.2.dist-info/RECORD,,
pycocoevalcap-1.2.dist-info/REQUESTED,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
pycocoevalcap-1.2.dist-info/WHEEL,sha256=EVRjI69F5qVjm_YgqcTXPnTAv3BfSUr0WVAHuSP3Xoo,92
pycocoevalcap-1.2.dist-info/top_level.txt,sha256=RyYnf7uoaylvdcX-U7xSBn6y_lLOSqBXqop27hWUvHk,14
pycocoevalcap/__pycache__/eval.cpython-311.pyc,,
pycocoevalcap/bleu/__pycache__/bleu.cpython-311.pyc,,
pycocoevalcap/bleu/__pycache__/bleu_scorer.cpython-311.pyc,,
pycocoevalcap/bleu/bleu.py,sha256=BbdKkrMuBx6s-5VO80MoE3z9G4LFY1cmSGMwh97yN3Y,1263
pycocoevalcap/bleu/bleu_scorer.py,sha256=QTzP73G4jF8R0l0F_c8IYloVWTOP-7fCeH6AARbq7N0,8618
pycocoevalcap/cider/__pycache__/cider.cpython-311.pyc,,
pycocoevalcap/cider/__pycache__/cider_scorer.cpython-311.pyc,,
pycocoevalcap/cider/cider.py,sha256=JotxywlszRDPSnzUmppyByABki8YdvToiDPh60ZJz8s,1669
pycocoevalcap/cider/cider_scorer.py,sha256=KZA3g4NzORwzNeBmyGe6LW3YYX0sj7LEGT8yYgp3Stw,7681
pycocoevalcap/eval.py,sha256=LTo72Lcq76n5z-JCddvs1OnP97qRd83VX2iWJfNysZM,2684
pycocoevalcap/example/__pycache__/coco_eval_example.cpython-311.pyc,,
pycocoevalcap/example/coco_eval_example.py,sha256=LKo-FfAPJI6ZtF1bTyPT4M6H8rXaIIrYLQJJEe4oy9k,847
pycocoevalcap/meteor/__pycache__/meteor.cpython-311.pyc,,
pycocoevalcap/meteor/data/paraphrase-en.gz,sha256=wUesfSyR8vuzrTHks1IjUGHrgxReBDTa8hfunKWXX0g,61813011
pycocoevalcap/meteor/meteor-1.5.jar,sha256=Hle0xywIMOvmhVjxx5mmJOlsvBtgRcn2Mw4m3P9ur8I,6318693
pycocoevalcap/meteor/meteor.py,sha256=Wsmi9tfh4ElX36IHdJd_20nGt3pDxD1PnrjyFbHv2FA,3123
pycocoevalcap/rouge/__pycache__/rouge.cpython-311.pyc,,
pycocoevalcap/rouge/rouge.py,sha256=JPH9ejuH59nE_VqtqIx59UpY1djTbJOCqvenKpHt_HQ,3625
pycocoevalcap/spice/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
pycocoevalcap/spice/__pycache__/__init__.cpython-311.pyc,,
pycocoevalcap/spice/__pycache__/get_stanford_models.cpython-311.pyc,,
pycocoevalcap/spice/__pycache__/spice.cpython-311.pyc,,
pycocoevalcap/spice/get_stanford_models.py,sha256=y0gRIlmmrsVKL6t8NCaJ7HaBW-5FrCoD0L9FqzcRzqM,1770
pycocoevalcap/spice/lib/Meteor-1.5.jar,sha256=AUo2J9J3pvS6W0SiBzP43_KLu0KCBMNqv9pWVlZYugk,6318623
pycocoevalcap/spice/lib/SceneGraphParser-1.0.jar,sha256=g6G3w_iW3RNflTm6QdZbFVgn38MSiTv9W7g1yfmuMQo,164039
pycocoevalcap/spice/lib/ejml-0.23.jar,sha256=EtROgf3WYa0qrGOEUR-s89j6ADw86NnOxfIoJ9GBmiY,301506
pycocoevalcap/spice/lib/fst-2.47.jar,sha256=jIcf68hZz3Lb7RxsHuvFYAw9k8Pphv7M9GI8xQhgmMw,380882
pycocoevalcap/spice/lib/guava-19.0.jar,sha256=WNTMLgXrsBK7rFaLAy91Yjvhy2-wlvPGDHKob38FfeQ,2308517
pycocoevalcap/spice/lib/hamcrest-core-1.3.jar,sha256=Zv3vkelzk0jfeglqo4SlaF9Oh1WEzOiThqekclHE2Ok,45024
pycocoevalcap/spice/lib/jackson-core-2.5.3.jar,sha256=yakAwwxwLTcb4tNxSTjfpt5hZZnI6zn448967dYt_TI,229998
pycocoevalcap/spice/lib/javassist-3.19.0-GA.jar,sha256=0Zwe9DzNnLGzlGa7LxyORcK2dS8eE6PftgCWVD0Xkfo,749499
pycocoevalcap/spice/lib/json-simple-1.1.1.jar,sha256=TmlpaJK4i0HFXUmrL9zCHurZK_VKzFiMAFBZbDt1GZw,23931
pycocoevalcap/spice/lib/junit-4.12.jar,sha256=WXIfCAXiI9hLkGd4h9n_Vn3FNNfFAsqQPAwrF_BcEWo,314932
pycocoevalcap/spice/lib/lmdbjni-0.4.6.jar,sha256=9OjFHrTvBavlfSGH529cM15O8W2FAzKet9vFrnqQUpw,85980
pycocoevalcap/spice/lib/lmdbjni-linux64-0.4.6.jar,sha256=C3dz24c6ruw5mlARPApOEh3w7_XnJGB_e8gXFFIQfZQ,385952
pycocoevalcap/spice/lib/lmdbjni-osx64-0.4.6.jar,sha256=fWZ3gDe1aJ58ba8ipxQHIvIIXaSuR5vO2kpL5IfVVGA,106300
pycocoevalcap/spice/lib/lmdbjni-win64-0.4.6.jar,sha256=lpxqpZiTuChWpoMtpkBnKw8j8dcvcNewQpbHGXDXW3k,72671
pycocoevalcap/spice/lib/objenesis-2.4.jar,sha256=CQQW4WCn45j3ngFJp7Be8yixhDiYo04IwcwXASjTJtA,51287
pycocoevalcap/spice/lib/slf4j-api-1.7.12.jar,sha256=Cu6ad6SUDXKTKw0NlVd5P4cuZqA_WY5HP0Xn7-zcz5k,32127
pycocoevalcap/spice/lib/slf4j-simple-1.7.21.jar,sha256=ogF5zD3tLX4vXe9MyzSy0INuL4yeeTUqa6vWplWcyOA,10905
pycocoevalcap/spice/spice-1.0.jar,sha256=bGpzrm3RCGZwR8GBhwdZBBOTTcufGmLO9kaQWbQbPVk,19751099
pycocoevalcap/spice/spice.py,sha256=RZW0xGiUUW7R7dCdOcoCV0_ghk6hD6C45QSxWTd3thU,3021
pycocoevalcap/tokenizer/__pycache__/ptbtokenizer.cpython-311.pyc,,
pycocoevalcap/tokenizer/ptbtokenizer.py,sha256=3tkFURJ7ikv2g4c0MsyvqDMwqZl_bQtpXLcaYYgIuME,2858
pycocoevalcap/tokenizer/stanford-corenlp-3.4.1.jar,sha256=L8uRu3oRH5PXHiZPTuDjr9GboN3m0hs4YFCI356UA5k,5921410
