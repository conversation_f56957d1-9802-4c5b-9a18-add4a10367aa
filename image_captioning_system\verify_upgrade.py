#!/usr/bin/env python3
"""
Simple verification script for the Gemini model upgrade.
"""

import os
import sys

# Add the current directory to the path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def main():
    print("🔧 Gemini Model Upgrade Verification")
    print("=" * 40)
    
    try:
        # Check config
        from config import GEMINI_MODEL_NAME
        print(f"✅ Config model: {GEMINI_MODEL_NAME}")
        
        # Check gemini adapter default
        from src.models.gemini_adapter import GeminiImageCaptioner
        import inspect
        
        # Get the default value from the constructor
        sig = inspect.signature(GeminiImageCaptioner.__init__)
        default_model = sig.parameters['model_name'].default
        print(f"✅ Adapter default: {default_model}")
        
        # Verify they match
        if GEMINI_MODEL_NAME == default_model == "gemini-2.5-flash":
            print("🎉 SUCCESS: Model upgrade completed!")
            print("   Both config and adapter are using gemini-2.5-flash")
            return True
        else:
            print("❌ MISMATCH: Configuration inconsistency detected")
            return False
            
    except Exception as e:
        print(f"❌ Error: {e}")
        return False

if __name__ == "__main__":
    success = main()
    print(f"\nResult: {'PASS' if success else 'FAIL'}")
